import 'dart:io';
import 'dart:math';

import 'package:cbrs/core/common/models/transaction_success_model.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:confetti/confetti.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:lottie/lottie.dart';

class CustomSuccessTransactionBottomSheet extends StatefulWidget {
  /// Creates a customizable transaction success bottom sheet
  ///
  /// [data] contains the transaction details
  /// [transactionType] defines the type of transaction
  /// [onContinue] is called when the user taps "Back To Home"
  const CustomSuccessTransactionBottomSheet({
    required this.onContinue,
    required this.data,
    required this.originalCurrency,
    required this.totalAmount,
    required this.billAmount,

    this.status = 'Paid',
    this.isFromChat = true,
    this.transactionType = 'bank_transfer',
    super.key,
    this.title = '',
    this.showActionButtons = true,
  });

  final VoidCallback onContinue;
  final Map<String, dynamic> data;
  final String transactionType;
  final String title;
  final bool showActionButtons;

  final bool isFromChat;
  final String status;
  final String originalCurrency;
  final double totalAmount; // TODO,
  final double billAmount; // TODO,


  @override
  State<CustomSuccessTransactionBottomSheet> createState() =>
      _CustomSuccessTransactionBottomSheetState();
}

class _CustomSuccessTransactionBottomSheetState
    extends State<CustomSuccessTransactionBottomSheet>
    with TickerProviderStateMixin {
  late final AnimationController _controller;

  double _heightFraction = 0.85;
  late ConfettiController _confettiController;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(vsync: this);

    Future.delayed(const Duration(seconds: 2), () {
      setState(() {
        _heightFraction = 1.0; // Expand to full screen
      });
    });

    // Only auto-close if explicitly from chat
    // For money requests and other transactions, we want the user to manually close
    if (widget.isFromChat &&
        widget.transactionType != 'money_request' &&
        widget.transactionType != 'mortgage_loan') {
      debugPrint('Setting up auto-close for ${widget.transactionType}');
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          debugPrint('Auto-closing bottom sheet');
          Navigator.pop(context, true);
        }
      });
    } else {
      debugPrint(
        'NOT auto-closing (isFromChat=${widget.isFromChat}, transactionType=${widget.transactionType})',
      );
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;

    return BlocListener<TransactionBloc, TransactionState>(
      listener: (context, state) {
        if (state is ReceiptDownloadLoading) {
          CustomToastification(
            context,
            message: 'Downloading receipt...',
            successTitle: 'WAITING',
            isError: false,
          );
        } else if (state is ReceiptDownloadError) {
          CustomToastification(
            context,
            message: 'Error downloading receipt: ${state.message}',
          );
        }
      },
      child: PopScope(
        // Allow popping but navigate to home when popped
        onPopInvokedWithResult: (bool didPop, dynamic result) {
          if (didPop) {
            context.goNamed(AppRouteName.home);
          }
        },
        child: SafeArea(
          bottom: false,
          child: AnimatedContainer(
            clipBehavior: Clip.antiAlias,
            decoration: const ShapeDecoration(
              color: Color(0xFFFCFCFC),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(36),
                  topRight: Radius.circular(36),
                ),
              ),
            ),
            duration: const Duration(milliseconds: 600),
            curve: Curves.easeInOut,
            height: screenHeight * _heightFraction,
            child: Column(
              children: [
                Flexible(
                  child: Column(
                    children: [
                      if (_heightFraction < 1)
                        Center(
                          child: Container(
                            width: 64,
                            height: 6,
                            margin: EdgeInsets.only(
                              top: 16.h,
                              left: 16.w,
                              right: 16.w,
                              bottom: 8,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(0xFFBCBCBC),
                              borderRadius: BorderRadius.circular(40),
                            ),
                          ),
                        ),
                      Flexible(
                        child: SingleChildScrollView(
                          physics: const ClampingScrollPhysics(),
                          child: Container(
                            color: Colors.white,
                            padding: EdgeInsets.only(
                              left: 16.w,
                              right: 16.w,
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                if (_heightFraction < 1)
                                  const SizedBox(
                                    height: 8,
                                  )
                                else
                                  SizedBox(
                                    height: Platform.isAndroid ? 40 : 56,
                                  ),
                                Row(
                                  children: [
                                    Expanded(
                                      child: Container(
                                        padding: EdgeInsets.only(
                                          top: 11.h,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius:
                                              BorderRadius.circular(32),
                                          boxShadow: [
                                            BoxShadow(
                                              color: Colors.black.withAlpha(15),
                                              blurRadius: 100,
                                            ),
                                          ],
                                        ),
                                        child: Stack(
                                          alignment: Alignment.center,
                                          children: [
                                            Positioned.fill(
                                              child: SizedBox.expand(
                                                child: Lottie.asset(
                                                  MediaRes.confettiFile,
                                                  fit: BoxFit.fill,
                                                  repeat: false,
                                                  onLoaded: (composition) {
                                                    _controller
                                                      ..duration =
                                                          composition.duration
                                                      ..forward();
                                                  },
                                                ),
                                              ),
                                            ),
                                            Column(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Image.asset(
                                                  'assets/images/success_image.png',
                                                  height: 112.h,
                                                  width: 112.w,
                                                ),
                                                Container(
                                                  margin: const EdgeInsets
                                                      .symmetric(
                                                    horizontal: 12,
                                                  ),
                                                  padding: EdgeInsets.symmetric(
                                                    vertical: 8.w,
                                                  ),
                                                  decoration:
                                                      const BoxDecoration(
                                                    color: Colors.white,
                                                  ),
                                                  child: Column(
                                                    children: [
                                                      CustomBuildText(
                                                        text:
                                                            '${AppMapper.safeFormattedNumberWithDecimal(widget.billAmount)} ${widget.originalCurrency}',
                                                        textAlign:
                                                            TextAlign.center,
                                                        fontSize: 30.sp,
                                                        fontWeight:
                                                            FontWeight.w800,
                                                        color: const Color(
                                                          0xFF3AB73A,
                                                        ),
                                                      ),
                                                      SizedBox(height: 4.h),
                                                      Padding(
                                                        padding:
                                                            EdgeInsets.only(
                                                          left: 16.w,
                                                          right: 16.w,
                                                        ),
                                                        child: CustomBuildText(
                                                          text: widget.title,
                                                          textAlign:
                                                              TextAlign.center,
                                                          fontSize: 14.sp,
                                                          caseType: 'default',
                                                          color: const Color(
                                                            0xFF6D6D6D,
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                const SizedBox(
                                                  height: 16,
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  height: 12.h,
                                ),
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 10.w,
                                    vertical: 16.h,
                                  ),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFFF8F8F8),
                                    borderRadius: BorderRadius.circular(24),
                                  ),
                                  child: Column(
                                    children: [
                                      Row(
                                        children: [
                                          CustomBuildText(
                                            text: 'Transaction Detail',
                                            textAlign: TextAlign.center,
                                            fontSize: 14.sp,
                                            fontWeight: FontWeight.w700,
                                            caseType: 'default',
                                          ),
                                        ],
                                      ),
                                      SizedBox(
                                        height: 10.h,
                                      ),
                                      Container(
                                        padding: const EdgeInsets.all(10),
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius:
                                              BorderRadius.circular(24),
                                        ),
                                        child: Column(
                                          spacing: 10,
                                          children: [
                                            Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                vertical: 4,
                                                horizontal: 4,
                                              ),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  CustomBuildText(
                                                    text: 'Status',
                                                    textAlign: TextAlign.center,
                                                    fontSize: 14.sp,
                                                    caseType: 'default',
                                                    color:
                                                        const Color(0xFF00B235),
                                                  ),
                                                  Container(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                      horizontal: 14.w,
                                                      vertical: 6.h,
                                                    ),
                                                    decoration: BoxDecoration(
                                                      color: const Color(
                                                        0xFFE0FFE1,
                                                      ),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                        32,
                                                      ),
                                                    ),
                                                    child: CustomBuildText(
                                                      text: widget.status,
                                                      textAlign:
                                                          TextAlign.center,
                                                      fontSize: 13.sp,
                                                      caseType: 'default',
                                                      fontWeight:
                                                          FontWeight.w600,
                                                      color: const Color(
                                                        0xFF00B235,
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),

                                            // Dynamic details from model
                                            ...widget.data.entries.map(
                                              (entry) => _buildTransactionList(
                                                label: entry.key,
                                                value: entry.value.toString(),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(
                                        height: 14.h,
                                      ),
                                      Container(
                                        padding: const EdgeInsets.all(10),
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius:
                                              BorderRadius.circular(14),
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            CustomBuildText(
                                              text: 'Total Amount',
                                              textAlign: TextAlign.center,
                                              fontSize: 16.sp,
                                              fontWeight: FontWeight.w700,
                                              caseType: 'default',
                                            ),
                                            CustomBuildText(
                                              text:
                                                  '${AppMapper.safeFormattedNumberWithDecimal(widget.totalAmount)} ${widget.originalCurrency}',
                                              textAlign: TextAlign.center,
                                              fontSize: 16.sp,
                                              fontWeight: FontWeight.w700,
                                              caseType: 'default',
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(
                                  height: 12.h,
                                ),
                                if (_heightFraction > 0.9 &&
                                    widget.showActionButtons)
                                  _buildActionButtons(context),
                                SizedBox(
                                  height: 38.h,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.only(
                    top: 12.h,
                    left: 16.w,
                    right: 16.w,
                    bottom: Platform.isAndroid ? 4 : 24.h,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(15),
                        blurRadius: 8,
                        offset: const Offset(0, -1),
                      ),
                    ],
                  ),
                  child: CustomRoundedBtn(
                    btnText: 'Back To Home',
                    isLoading: false,
                    onTap: () {
                      if (Navigator.canPop(context)) context.pop();
                      context.goNamed(AppRouteName.home);
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTransactionList({
    required String label,
    required String value,
  }) {
    return value.isEmpty
        ? const SizedBox.shrink()
        : Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomBuildText(
                text: label,
                textAlign: TextAlign.center,
                fontSize: 14.sp,
                caseType: 'default',
                color: const Color(0xFF979797),
              ),
              const SizedBox(
                width: 20,
              ),
              Flexible(
                child: CustomBuildText(
                  text: value,
                  textAlign: TextAlign.right,
                  fontSize: 14.sp,
                  caseType: 'default',
                ),
              ),
            ],
          );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
      decoration: BoxDecoration(
        color: const Color(0xFFF9F9F9),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          _actionButton(
            icon: MediaRes.qrReceiptIcon,
            label: 'QR receipt',
            onTap: () {},
          ),
          _actionButton(
            icon: MediaRes.sharingIcon,
            label: 'Share',
            onTap: () {},
          ),
          _actionButton(
            icon: MediaRes.screenshotIcon,
            label: 'Screenshot',
            onTap: () {},
          ),
          _actionButton(
            icon: MediaRes.getReceiptIcon,
            label: 'Get receipt',
            onTap: () {
              debugPrint('Downloading receipt');
              final billRefNo = widget.data['billRefNo'] as String? ??
                  (widget.data['transaction'] != null
                      ? widget.data['transaction']['billRefNo'] as String?
                      : null);

              _downloadReceipt(context, billRefNo);
            },
          ),
        ],
      ),
    );
  }

  Widget _actionButton({
    required String icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Column(
          children: [
            Image.asset(
              icon,
              width: 40,
            ),
            SizedBox(
              height: 3.h,
            ),
            CustomBuildText(text: label),
          ],
        ),
      ),
    );
  }

  void _downloadReceipt(BuildContext context, String? billRefNo) {
    if (billRefNo != null) {
      context.read<TransactionBloc>().add(
            DownloadReceiptEvent(billRefNo: billRefNo),
          );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Receipt not available for this transaction'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
