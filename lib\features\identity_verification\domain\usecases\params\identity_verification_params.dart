import 'dart:io';
import 'package:equatable/equatable.dart';

class DocumentUploadParams extends Equatable {
  const DocumentUploadParams({
    required this.frontPhoto,
    required this.backPhoto,
    required this.selfiePhoto,
    this.documentType = 'id_card',
  });

  final File frontPhoto;
  final File backPhoto;
  final File selfiePhoto;
  final String documentType;

  @override
  List<Object?> get props => [frontPhoto, backPhoto, selfiePhoto, documentType];

  DocumentUploadParams copyWith({
    File? frontPhoto,
    File? backPhoto,
    File? selfiePhoto,
    String? documentType,
  }) {
    return DocumentUploadParams(
      frontPhoto: frontPhoto ?? this.frontPhoto,
      backPhoto: backPhoto ?? this.backPhoto,
      selfiePhoto: selfiePhoto ?? this.selfiePhoto,
      documentType: documentType ?? this.documentType,
    );
  }
}
