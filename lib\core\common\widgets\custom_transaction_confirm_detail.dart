import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:googleapis/monitoring/v3.dart';

class CustomTransactionConfirmDetail extends StatelessWidget {
  const CustomTransactionConfirmDetail({
    required this.amount,
    required this.child,
    super.key,
    this.isDollar = true,
  });
  final String amount;
  final Widget child;
  final bool isDollar;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: _buildAmount(),
          ),
          _buildSize(height: 36.h),
          CustomBuildText(
            text: 'Transaction Details',
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
          child,
        ],
      ),
    );
  }

  Widget _buildAmount() {
    return CustomBuildText(
      text: isDollar ? '\$ $amount' : '$amount ETB',
      fontSize: 36.sp,
      fontWeight: FontWeight.w700,
      caseType: '',
    );
  }

  Widget _buildSize({double? width, double? height}) {
    return SizedBox(
      width: width,
      height: height,
    );
  }
}
