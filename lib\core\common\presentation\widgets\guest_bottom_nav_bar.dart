import 'package:cbrs/core/extensions/context_extensions.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class GuestBottomNavBar extends StatelessWidget {
  const GuestBottomNavBar({
    required this.currentIndex,
    required this.onTap,
    super.key,
  });

  final int currentIndex;
  final Function(int) onTap;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        bottom: true,
        child: Container(
          decoration: BoxDecoration(
            color: context.theme.cardColor,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: Container(
            height: 88.h,
            padding: EdgeInsets.symmetric(horizontal: 8.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildNavItem(
                  context,
                  HugeIcons.strokeRoundedHome02,
                  HugeIcons.strokeRoundedHome02,
                  'Home',
                  0,
                  hasCustomIcon: true,
                  customActiveIconLocation: MediaRes.activeHome,
                  customInActiveIconLocation: MediaRes.inactiveHome,
                ),
                _buildNavItem(
                  context,
                  HugeIcons.strokeRoundedAppStore,
                  HugeIcons.strokeRoundedAppStore,
                  'Mini Apps',
                  1,
                  hasCustomIcon: true,
                  customActiveIconLocation: MediaRes.activeMiniApps,
                  customInActiveIconLocation: MediaRes.inactiveMiniApps,
                ),
                _buildNavItem(
                  context,
                  HugeIcons.strokeRoundedBubbleChat,
                  HugeIcons.strokeRoundedBubbleChat,
                  'Chat',
                  2,
                  hasCustomIcon: true,
                  customActiveIconLocation: MediaRes.activeChat,
                  customInActiveIconLocation: MediaRes.inactiveChat,
                ),
                _buildNavItem(
                  context,
                  FluentIcons.history_24_regular,
                  FluentIcons.history_24_regular,
                  'Transactions',
                  3,
                  hasCustomIcon: true,
                  customActiveIconLocation: MediaRes.activeTransaction,
                  customInActiveIconLocation: MediaRes.inactiveTransactionc,
                ),
                _buildNavItem(
                  context,
                  FluentIcons.person_24_regular,
                  FluentIcons.person_24_regular,
                  'Profile',
                  4,
                  hasCustomIcon: true,
                  customActiveIconLocation: MediaRes.activePerson,
                  customInActiveIconLocation: MediaRes.inactivePerson,
                ),
              ],
            ),
          ),
        ));
  }

  Widget _buildNavItem(
    BuildContext context,
    IconData icon,
    IconData activeIcon,
    String label,
    int index, {
    bool hasCustomIcon = false,
    String customActiveIconLocation = '',
    String customInActiveIconLocation = '',
  }) {
    final isSelected = currentIndex == index;
    final theme = Theme.of(context);

    return Expanded(
      child: GestureDetector(
        onTap: () => onTap(index),
        behavior: HitTestBehavior.opaque,
        child: Container(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 200),
                child: hasCustomIcon
                    ? ShaderMask(
                        shaderCallback: (Rect bounds) {
                          return LinearGradient(
                            colors: [
                              isSelected
                                  ? theme.primaryColor
                                  : theme.unselectedWidgetColor,
                              isSelected
                                  ? theme.primaryColor
                                  : theme.unselectedWidgetColor,
                            ],
                          ).createShader(bounds);
                        },
                        child: Image.asset(
                          width: 24.w,
                          height: 24.h,
                          isSelected
                              ? customActiveIconLocation
                              : customInActiveIconLocation,
                          color: Colors.white,
                          key: ValueKey(isSelected),
                        ),
                      )
                    : ShaderMask(
                        shaderCallback: (Rect bounds) {
                          return LinearGradient(
                            colors: [
                              isSelected
                                  ? theme.primaryColor
                                  : theme.unselectedWidgetColor,
                              isSelected
                                  ? theme.primaryColor
                                  : theme.unselectedWidgetColor,
                            ],
                          ).createShader(bounds);
                        },
                        child: Icon(
                          isSelected ? activeIcon : icon,
                          size: 24,
                          color: Colors.white,
                          key: ValueKey(isSelected),
                        ),
                      ),
              ),
              const SizedBox(height: 4),
              AnimatedDefaultTextStyle(
                duration: const Duration(milliseconds: 200),
                style: GoogleFonts.outfit(
                  fontSize: 12.sp,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  color: isSelected
                      ? theme.primaryColor
                      : theme.unselectedWidgetColor,
                ),
                child: Text(label),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
