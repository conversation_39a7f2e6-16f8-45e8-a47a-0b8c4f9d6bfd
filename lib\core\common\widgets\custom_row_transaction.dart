import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomRowTransaction extends StatelessWidget {
  const CustomRowTransaction({
    super.key,
    this.label,
    this.value,
    this.isLast = false,
    this.caseType = 'eachWord',
  });
  final String? label;
  final String? value;
  final bool isLast;
  final String caseType;

  @override
  Widget build(BuildContext context) {
    return value == null || value!.isEmpty
        ? Container()
        : _buildTransactionDetail();
  }

  String _formatCurrencyValue(String value) {
    // Handle ETB currency format
    final etbPattern = RegExp(
      r'([0-9,.]+)\s*(ETB|Etb|etb|eTb|EtB|eTB|etB)',
      caseSensitive: false,
    );
    // Handle USD with $ symbol
    final usdPattern = RegExp(r'\$\s*([0-9,.]+)');
    // Handle USD text format without $ symbol
    final usdTextPattern = RegExp(
      r'([0-9,.]+)\s*(USD|Usd|usd|uSd|UsD|uSD|usD)',
      caseSensitive: false,
    );

    if (etbPattern.hasMatch(value)) {
      return value.replaceAllMapped(
        etbPattern,
        (match) => '${match.group(1)} ETB',
      );
    } else if (usdTextPattern.hasMatch(value)) {
      return value.replaceAllMapped(
        usdTextPattern,
        (match) => '${match.group(1)} USD',
      );
    } else if (usdPattern.hasMatch(value)) {
      return value.replaceAllMapped(
        usdPattern,
        (match) => '${match.group(1)} USD',
      );
    }

    return value;
  }

  Widget _buildTransactionDetail() {
    final formattedValue = _formatCurrencyValue(value!);
    final isEmail = label?.toLowerCase().contains('email') ?? false;

    return Container(
      padding: EdgeInsets.only(bottom: 16.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment:
            isEmail ? CrossAxisAlignment.start : CrossAxisAlignment.center,
        children: [
          CustomBuildText(
            text: '${_formatCurrencyValue(label!)}:',
            fontSize: isLast ? 16.sp : 14.sp,
            color: isLast
                ? Colors.black
                : const Color(0xFF000000).withOpacity(0.5),
            fontWeight: isLast ? FontWeight.w700 : FontWeight.w400,
            caseType: 'default',
          ),
          SizedBox(width: 12.w),
          Flexible(
            child: CustomBuildText(
              text: formattedValue,
              caseType: isLast ? 'all' : caseType,
              fontSize: isLast ? 20.sp : 14.sp,
              fontWeight: isLast ? FontWeight.w700 : FontWeight.w600,
              textAlign: TextAlign.end,
              overflow: isEmail ? null : TextOverflow.ellipsis,
              maxLines: isEmail ? null : 1,
              softWrap: isEmail,
            ),
          ),
        ],
      ),
    );
  }
}
