import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/res/media_res.dart';

class NoLinkedAccountWidget extends StatelessWidget {
  const NoLinkedAccountWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      children: [
        Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.w),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 24.w, vertical: 32.h),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16.r),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0x14000000),
                        blurRadius: 24,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Container(
                        width: 96.w,
                        height: 96.h,
                        padding: EdgeInsets.all(16.w),
                        decoration: BoxDecoration(
                          color: theme.primaryColor.withOpacity(.15),
                          shape: BoxShape.circle,
                        ),
                        child: Image.asset(
                          MediaRes.bank_three_d,
                          width: 64.w,
                          height: 64.h,
                          fit: BoxFit.contain,
                        ),
                      ),
                      SizedBox(height: 24.h),
                      Text(
                        'No Linked Bank Account',
                        style: GoogleFonts.outfit(
                          fontSize: 20.sp,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                          height: 1.2,
                        ),
                      ),
                      SizedBox(height: 12.h),
                      Text(
                        'Link your accounts from any local bank to easily add money to your wallet and make transfers.',
                        textAlign: TextAlign.center,
                        style: GoogleFonts.outfit(
                          fontSize: 14.sp,
                          color: const Color(0xFF666666),
                          height: 1.4,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.all(24.w),
          child: CustomButton(
            text: 'Link Bank Account',
            onPressed: () => context.pushNamed(AppRouteName.linkAccountMenu),
            options: CustomButtonOptions(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 16.h),
              color: theme.primaryColor,
              textStyle: GoogleFonts.outfit(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
              borderRadius: BorderRadius.circular(32.r),
            ),
          ),
        ),
      ],
    );
  }
}
