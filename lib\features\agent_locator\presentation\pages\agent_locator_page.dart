import 'dart:async';
import 'dart:ui' as ui;
import 'dart:typed_data';
import 'package:flutter/services.dart' show rootBundle;

import 'package:cbrs/core/location/location_service.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/features/agent_locator/presentation/widgets/agent_detail_sheet.dart';
import 'package:cbrs/features/agent_locator/presentation/widgets/agent_list_item.dart';
import 'package:cbrs/features/agent_locator/presentation/widgets/search_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:geolocator/geolocator.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class AgentLocatorPage extends StatefulWidget {
  const AgentLocatorPage({super.key});

  @override
  State<AgentLocatorPage> createState() => _AgentLocatorPageState();
}

class _AgentLocatorPageState extends State<AgentLocatorPage> {
  GoogleMapController? _mapController;
  bool _isMapReady = false;
  Set<Marker> _markers = {};

  // Mock agent data
  final List<Map<String, dynamic>> _agents = [
    {
      'id': '1',
      'name': 'Solomon Kebede',
      'position': const LatLng(9.0340, 38.7520),
      'role': 'Senior Agent',
      'phoneNumber': '+251912345678',
      'location': 'Bole Atlas',
      'address': 'Addis Ababa, Ethiopia',
      'imageUrl':
          'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d',
    },
    {
      'id': '2',
      'name': 'Abebe Bikila',
      'position': const LatLng(9.0310, 38.7540),
      'role': 'Agent',
      'phoneNumber': '+251987654321',
      'location': 'Kazanchis',
      'address': 'Adama, Ethiopia',
      'imageUrl':
          'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d',
    },
  ];

  // Add default location (can be your city center)
  LatLng _myLocation = const LatLng(9.0320, 38.7520); // Addis Ababa center
  StreamSubscription<Position>? _locationSubscription;

  final TextEditingController _searchController = TextEditingController();
  List<Map<String, dynamic>> _filteredAgents = [];
  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    _initializeMap();
    _initializeLocation();
    _filteredAgents = _agents;
  }

  void _initializeMap() {
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        setState(() {
          _isMapReady = true;
        });
      }
    });
  }

  Future<void> _initializeLocation() async {
    try {
      await _getCurrentLocation();
      await _initializeMarkers();
      _startLocationUpdates();
    } catch (e) {
      debugPrint('Error initializing location: $e');
    }
  }

  Future<void> _initializeMarkers() async {
    await _loadCustomMarkers();
  }

  Future<void> _loadCustomMarkers() async {
    try {
      // Create a custom bitmap descriptor for location marker
      final Uint8List myLocationBytes = await _resizeImageAsset(
        MediaRes.myLocationMarker,
        width: 144,
        height: 144,
      );

      final myLocationIcon = BitmapDescriptor.fromBytes(myLocationBytes);

      // Create a custom bitmap descriptor for agent marker
      final Uint8List agentBytes = await _resizeImageAsset(
        MediaRes.agentMarker,
        width: 240,
        height: 240,
      );

      final agentIcon = BitmapDescriptor.fromBytes(agentBytes);

      if (!mounted) return;

      final Set<Marker> newMarkers = {};

      // Add my location marker
      newMarkers.add(
        Marker(
          markerId: const MarkerId('my_location'),
          position: _myLocation,
          icon: myLocationIcon,
          zIndex: 2,
          anchor: const Offset(0.5, 1.0),
        ),
      );

      // Add agent markers
      for (var agent in _agents) {
        newMarkers.add(
          Marker(
            markerId: MarkerId(agent['id'] as String),
            position: agent['position'] as LatLng,
            icon: agentIcon,
            zIndex: 1,
            anchor: const Offset(0.5, 1.0),
            onTap: () => _showAgentDetails(agent),
          ),
        );
      }

      setState(() {
        _markers = newMarkers;
      });
    } catch (e) {
      debugPrint('Error loading markers: $e');
      // Handle error gracefully
    }
  }

  void _showAgentDetails(Map<String, dynamic> agent) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => AgentDetailsSheet(
        name: agent['name'] as String,
        imageUrl: agent['imageUrl'] as String,
        role: agent['role'] as String,
        phoneNumber: agent['phoneNumber'] as String,
        location: agent['location'] as String,
        address: agent['address'] as String,
      ),
    );
  }

  void _onSearchChanged(String query) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      setState(() {
        if (query.isEmpty) {
          _filteredAgents = _agents;
        } else {
          final lowercaseQuery = query.toLowerCase();
          _filteredAgents = _agents.where((agent) {
            return agent['name']
                    .toString()
                    .toLowerCase()
                    .contains(lowercaseQuery) ||
                agent['location']
                    .toString()
                    .toLowerCase()
                    .contains(lowercaseQuery) ||
                agent['address']
                    .toString()
                    .toLowerCase()
                    .contains(lowercaseQuery);
          }).toList();
        }
        _updateMarkersForFilteredAgents();
      });
    });
  }

  void _updateMarkersForFilteredAgents() async {
    final Set<Marker> newMarkers = {};

    // Create resized marker icons
    final Uint8List myLocationBytes = await _resizeImageAsset(
      MediaRes.myLocationMarker,
      width: 144,
      height: 144,
    );

    final myLocationIcon = BitmapDescriptor.fromBytes(myLocationBytes);

    final Uint8List agentBytes = await _resizeImageAsset(
      MediaRes.agentMarker,
      width: 240,
      height: 240,
    );

    final agentIcon = BitmapDescriptor.fromBytes(agentBytes);

    // Add user location marker
    newMarkers.add(Marker(
      markerId: const MarkerId('myLocation'),
      position: _myLocation,
      icon: myLocationIcon,
      zIndex: 2,
      anchor: const Offset(0.5, 1.0),
    ));

    // Add filtered agents markers
    for (final agent in _filteredAgents) {
      newMarkers.add(Marker(
        markerId: MarkerId(agent['id'] as String),
        position: agent['position'] as LatLng,
        icon: agentIcon,
        zIndex: 1,
        anchor: const Offset(0.5, 1.0),
        onTap: () => _showAgentDetails(agent),
      ));
    }

    setState(() {
      _markers = newMarkers;
    });

    // Adjust camera to show all markers
    if (_mapController != null && _filteredAgents.isNotEmpty) {
      final bounds = _calculateBounds(
          _filteredAgents.map((a) => a['position'] as LatLng).toList());
      _mapController!.animateCamera(CameraUpdate.newLatLngBounds(bounds, 50));
    }
  }

  LatLngBounds _calculateBounds(List<LatLng> positions) {
    double minLat = positions[0].latitude;
    double maxLat = positions[0].latitude;
    double minLng = positions[0].longitude;
    double maxLng = positions[0].longitude;

    for (final pos in positions) {
      if (pos.latitude < minLat) minLat = pos.latitude;
      if (pos.latitude > maxLat) maxLat = pos.latitude;
      if (pos.longitude < minLng) minLng = pos.longitude;
      if (pos.longitude > maxLng) maxLng = pos.longitude;
    }

    return LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );
  }

  Future<void> _getCurrentLocation() async {
    final position = await LocationService.getCurrentLocation();
    if (position != null && mounted) {
      setState(() {
        _myLocation = LatLng(position.latitude, position.longitude);
      });
      _updateMarkersWithLocation();
      _animateToCurrentLocation();
    }
  }

  void _startLocationUpdates() {
    _locationSubscription =
        LocationService.getLocationStream().listen((position) {
      if (mounted) {
        setState(() {
          _myLocation = LatLng(position.latitude, position.longitude);
        });
        _updateMarkersWithLocation();
      }
    });
  }

  void _animateToCurrentLocation() {
    if (_mapController != null && _myLocation != null) {
      _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(_myLocation!, 15),
      );
    }
  }

  void _updateMarkersWithLocation() async {
    if (_myLocation == null) return;

    final Uint8List myLocationBytes = await _resizeImageAsset(
      MediaRes.myLocationMarker,
      width: 144,
      height: 144,
    );

    final myLocationIcon = BitmapDescriptor.fromBytes(myLocationBytes);

    final Uint8List agentBytes = await _resizeImageAsset(
      MediaRes.agentMarker,
      width: 240,
      height: 240,
    );

    final agentIcon = BitmapDescriptor.fromBytes(agentBytes);

    final Set<Marker> newMarkers = {};

    // Add current location marker
    newMarkers.add(
      Marker(
        markerId: const MarkerId('my_location'),
        position: _myLocation!,
        icon: myLocationIcon,
        zIndex: 2,
        anchor: const Offset(0.5, 1.0),
      ),
    );

    // Add agent markers
    for (final agent in _filteredAgents) {
      newMarkers.add(
        Marker(
          markerId: MarkerId(agent['id'] as String),
          position: agent['position'] as LatLng,
          icon: agentIcon,
          zIndex: 1,
          anchor: const Offset(0.5, 1.0),
          onTap: () => _showAgentDetails(agent),
        ),
      );
    }

    setState(() {
      _markers = newMarkers;
    });
  }

  @override
  void dispose() {
    _mapController?.dispose();
    _searchController.dispose();
    _debounce?.cancel();
    _locationSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bottomInset = MediaQuery.of(context).viewInsets.bottom;

    return Scaffold(
      resizeToAvoidBottomInset: false, // Prevent default resize
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        title: Text(
          'Agent Locator',
          style: GoogleFonts.outfit(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: SafeArea(
        child: GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          child: Stack(
            children: [
              Column(
                children: [
                  Expanded(
                    child: Stack(
                      children: [
                        if (_isMapReady)
                          GoogleMap(
                            initialCameraPosition: CameraPosition(
                              target: _myLocation,
                              zoom: 15,
                            ),
                            onMapCreated: (controller) {
                              setState(() {
                                _mapController = controller;
                              });
                            },
                            markers: _markers,
                            zoomControlsEnabled: false,
                            mapToolbarEnabled: false,
                            myLocationButtonEnabled: false,
                            myLocationEnabled: true,
                          ),
                        if (!_isMapReady)
                          const Center(
                            child: CircularProgressIndicator(),
                          ),
                        AnimatedPositioned(
                          duration: const Duration(milliseconds: 200),
                          curve: Curves.easeOut,
                          bottom: bottomInset > 0 ? -280.h : 0,
                          left: 0,
                          right: 0,
                          child: Container(
                            height: 280.h,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(20.r),
                                topRight: Radius.circular(20.r),
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.05),
                                  blurRadius: 10,
                                  offset: const Offset(0, -5),
                                ),
                              ],
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 26.w, vertical: 8.h),
                                  child: Text(
                                    'Agents Near You',
                                    style: GoogleFonts.outfit(
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                                Container(
                                  height: 220.h,
                                  padding: EdgeInsets.symmetric(vertical: 4.h),
                                  child: ListView.builder(
                                    scrollDirection: Axis.horizontal,
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 16.w),
                                    itemCount: _filteredAgents.length,
                                    itemBuilder: (context, index) {
                                      final agent = _filteredAgents[index];
                                      return AgentListItem(
                                        name: agent['name'] as String,
                                        imageUrl: agent['imageUrl'] as String,
                                        role: agent['role'] as String,
                                        onTap: () => _showAgentDetails(agent),
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              // Overlay the search bar
              Positioned(
                top: 8.h,
                left: 0,
                right: 0,
                child: CustomSearchBar(
                  hintText: 'Search Near By Agents Stations',
                  onLocationTap: _animateToCurrentLocation,
                  onSearchChanged: _onSearchChanged,
                  controller: _searchController,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<Uint8List> _resizeImageAsset(String assetPath,
      {required int width, required int height}) async {
    ByteData data = await rootBundle.load(assetPath);
    ui.Codec codec = await ui.instantiateImageCodec(
      data.buffer.asUint8List(),
      targetWidth: width,
      targetHeight: height,
    );
    ui.FrameInfo fi = await codec.getNextFrame();
    return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
        .buffer
        .asUint8List();
  }
}
