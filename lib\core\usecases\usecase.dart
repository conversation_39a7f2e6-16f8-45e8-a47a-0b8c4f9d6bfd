import 'package:cbrs/core/utils/typedef.dart';
import 'package:dartz/dartz.dart';
import '../error/failures.dart';

abstract class UsecaseWithParams<Type, Params> {
  const UsecaseWithParams();
  ResultFuture<Type> call(Params params);
}

abstract class UsecaseWithoutParams<Type> {
  const UsecaseWithoutParams();
  ResultFuture<Type> call();
}

abstract class UseCase<Type, Params> {
  Future<Either<Failure, Type>> call(Params params);
}
