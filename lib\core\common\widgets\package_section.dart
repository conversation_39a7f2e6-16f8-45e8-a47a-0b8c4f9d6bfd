import 'package:cbrs/features/gift_packages/presentation/views/gift_packages_screen.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PackageSection extends StatelessWidget {
  const PackageSection({super.key});

  @override
  Widget build(BuildContext context) {
    final packages = [
      _PackageInfo(
        'Gift Packages',
        'Browse Gift Packages, purchase, and share the code for easy gifting.',
        MediaRes.giftImage,
        const LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: [Color(0xFFEDCBA4), Color(0xFFFFF1E0)],
            stops: [0, 1]),
      ),
      _PackageInfo(
        'Car Loans',
        'Browse our top car selection, apply for a loan, and drive away with ease.',
        MediaRes.carImage,
        const LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: [Color(0xFF9BD3EC), Color(0xFFCCEFFF)],
            stops: [0, 1]),
      ),
      _PackageInfo(
        'Mortgage Loans',
        'Explore our best homes and apartments, apply for a loan, and move in easily.',
        MediaRes.apartmentImage,
        const LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: [Color(0xFF9CE5AF), Color(0xFFD5FFE0)],
            stops: [0, 1]),
        // textColor: Colors.white,
      ),
      //  _PackageInfo(
      //   'Donations',
      //   'Explore donation options, contribute, and share the impact with those in need.',
      //   MediaRes.donationIcon,
      //   const LinearGradient(
      //     begin: Alignment.centerLeft,
      //     end: Alignment.centerRight,
      //     colors: [Color(0xFFF1B0ED), Color(0xFFFFE8FF)],
      //     stops: [0,1]
      //   ),
      //   // textColor: Colors.white,
      // ),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Package & Loans',
          style:
              GoogleFonts.outfit(fontSize: 16.sp, fontWeight: FontWeight.w600),
        ),
        SizedBox(
          height: 8.h,
        ),
        ...packages.map((p) => Padding(
              padding: EdgeInsets.only(bottom: 16.h),
              child: _PackageCard(
                title: p.title,
                description: p.description,
                icon: p.icon,
                gradient: p.gradient,
                textColor: p.textColor,
              ),
            )),
      ],
    );
  }
}

class _PackageInfo {
  final String title;
  final String description;
  final String icon;
  final Gradient gradient;
  final Color? textColor;

  _PackageInfo(this.title, this.description, this.icon, this.gradient,
      {this.textColor});
}

class _PackageCard extends StatelessWidget {
  final String title;
  final String description;
  final String icon;
  final Gradient gradient;
  final Color? textColor;

  const _PackageCard({
    required this.title,
    required this.description,
    required this.icon,
    required this.gradient,
    this.textColor,
  });

  void _handleNavigation(BuildContext context) {
    switch (title) {
      case 'Gift Packages':
        context.pushNamed(AppRouteName.giftPackages);
      case 'Car Loans':
        context.pushNamed(AppRouteName.carLoan);
      case 'Mortgage Loans':
        context.pushNamed(AppRouteName.mortgageLoanPage);
      case 'Donations':
        context.pushNamed(AppRouteName.donations);
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _handleNavigation(context),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 10.h),
        decoration: BoxDecoration(
          image: DecorationImage(
              image: AssetImage(
                MediaRes.dotPattern,
              ),
              fit: BoxFit.fill),
          gradient: gradient,
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              width: 84.w,
              height: 84.w,
              padding: EdgeInsets.all(4.h),
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10.r,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Image.asset(icon),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.outfit(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w700,
                      color: Colors.black,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    description,
                    style: GoogleFonts.outfit(
                        fontSize: 12.sp,
                        color: Color(0xFF000000).withOpacity(0.4),
                        fontWeight: FontWeight.w400
                        // height: 1
                        ),
                  ),
                  SizedBox(height: 10.h),
                  GestureDetector(
                    onTap: () => _handleNavigation(context),
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 16.w,
                        vertical: 8.h,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(width: 1, color: Color(0xFF065234)),
                      ),
                      child: Text(
                        'View All',
                        style: GoogleFonts.outfit(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF065234),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
