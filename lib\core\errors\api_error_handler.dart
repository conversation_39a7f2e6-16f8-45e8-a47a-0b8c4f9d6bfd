import 'package:cbrs/core/services/navigation/navigation_service.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:get/get.dart' hide Response;
import 'package:cbrs/core/services/routes/route.dart' show router;

import 'package:go_router/go_router.dart';
import 'package:googleapis/admob/v1.dart';

class ApiErrorHandler {
  NavigationService? _navigationService;
  final bool _isNavigating = false;

  NavigationService get navigationService {
    _navigationService ??= Get.find<NavigationService>();
    return _navigationService!;
  }

  static Exception handleError(DioException error) {
    debugPrint(
        'HET ERROR: ${error.type} ${error.message} the object  is ${error.response?.data}');

    if (_isTimeoutError(error.type)) {
      return const TimeoutException(
        message: 'Connection timed out. Please try again.',
      );
    }

    if (error.type == DioExceptionType.connectionError) {
      return const NetworkException(
        message:
            'No internet connection. Please check your connection and try again.',
      );
    }

    if (error.type == DioExceptionType.badResponse) {
      return _handleResponseError(error.response);
    }

    return ApiException(
      message: error.message ?? 'An unexpected error occurred gh',
      statusCode: error.response?.statusCode ?? 500,
    );
  }

  static bool _isTimeoutError(DioExceptionType type) {
    return type == DioExceptionType.connectionTimeout ||
        type == DioExceptionType.sendTimeout ||
        type == DioExceptionType.receiveTimeout;
  }

  static Exception _handleResponseError(Response<dynamic>? response) {
    final message = _extractErrorMessage(response);
    final errors = _extractErrorDetails(response);
    final statusCode = response?.statusCode ?? 500;

    return _createExceptionFromStatus(statusCode, message, errors);
  }

  static String? _extractErrorMessage(Response<dynamic>? response) {
    return response?.data is Map ? response?.data['message'] as String? : null;
  }

  static Map<String, dynamic>? _extractErrorDetails(
    Response<dynamic>? response,
  ) {
    return response?.data is Map
        ? response?.data['errors'] as Map<String, dynamic>?
        : null;
  }

  static void navigateToLoginScreen() {
    final context = router.routerDelegate.navigatorKey.currentContext;
    if (context == null) return;

    final currentLocation =
        router.routerDelegate.currentConfiguration.uri.toString();
    if (currentLocation == AppRouteName.tokenDeviceLogin) return;

    router
        .pushNamed(AppRouteName.tokenDeviceLogin)
        .onError((error, stackTrace) {
      return null;
    });
  }

  static Exception _createExceptionFromStatus(
    int statusCode,
    String? message,
    Map<String, dynamic>? errors,
  ) {
    debugPrint('ERROR _createExceptionFromStatus and the message is $message');

    switch (statusCode) {
      case 400:
        return ValidationException(message: message, errors: errors);
      case 401:
        navigateToLoginScreen();
        return UnauthorizedException(message: message);
      case 403:
        return ForbiddenException(message: message);
      case 404:
        return NotFoundException(message: message);
      case 429:
        return RateLimitException(message: message);
      case 500:
        return ServerException(
          message: message ?? 'Internal server error',
          statusCode: statusCode,
        );
      default:
        return ApiException(
          message: message ?? 'Unknown error occurred',
          statusCode: statusCode,
        );
    }
  }
}
