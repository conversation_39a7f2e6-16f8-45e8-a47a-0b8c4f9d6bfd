String getNameInitial(String firstName, String lastName) =>
    '${firstName.isNotEmpty ? firstName[0] : ''}'
    '${lastName.isNotEmpty ? lastName[0] : ''}';

String separateNames(String value) {
  if (value.isEmpty) return '';

  final names = value.trim().split(' ');

  if (names.isEmpty) return '';

  if (names.length > 1) {
    return '${names.first[0]}${names.last[0]}'.toUpperCase();
  }

  return names.first[0];
}
