import 'package:cbrs/core/services/storage/hive_box_manager.dart';
import 'package:flutter/foundation.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:android_id/android_id.dart';

class DeviceService {
  
  DeviceService({
    required HiveBoxManager hiveManager,
    DeviceInfoPlugin? deviceInfo,
  }) : _hiveManager = hiveManager,
       _deviceInfo = deviceInfo ?? DeviceInfoPlugin();
  static const _deviceIdKey = 'device_uuid';
  final HiveBoxManager _hiveManager;
  final DeviceInfoPlugin _deviceInfo;
  static const AndroidId _androidIdPlugin = AndroidId();

  Future<String> getDeviceId() async {
    try {
      var deviceId = _hiveManager.mainBox.get(_deviceIdKey) as String?;
      
      if (deviceId != null) {
        return deviceId;
      }
      
      deviceId = await _getPlatformSpecificDeviceId();
      await _hiveManager.mainBox.put(_deviceIdKey, deviceId);
      
      return deviceId;
    } catch (e) {
      rethrow;
    }
  }

  Future<String> _getPlatformSpecificDeviceId() async {
    try {
      // for android devices
      if (defaultTargetPlatform == TargetPlatform.android) {
        final androidId = await _androidIdPlugin.getId() ?? '';
        return androidId.isNotEmpty ? androidId : 'unknown';
      } 
      
      // for ios devices

      if (defaultTargetPlatform == TargetPlatform.iOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        final deviceId = iosInfo.identifierForVendor ?? '';
        return deviceId.isNotEmpty ? deviceId : 'unknown';
      }
      
      return 'unknown';
    } catch (e) {
      return 'unknown';
    }
  }

}
