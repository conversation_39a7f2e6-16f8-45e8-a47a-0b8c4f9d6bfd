import 'package:cbrs/core/res/media_res.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomPasswordPad extends StatefulWidget {
  const CustomPasswordPad({
    required this.controller,
    required this.onChanged,
    required this.isLoading,
    required this.onSubmitted,
    super.key,
  });

  final TextEditingController controller;
  final void Function(String, bool) onChanged;
  final void Function(String) onSubmitted;

  final bool isLoading;

  @override
  State<CustomPasswordPad> createState() => _CustomPasswordPadState();
}

class _CustomPasswordPadState extends State<CustomPasswordPad> {
  void _onKeyPressed(String keys, {bool isKey = true}) {
    if (widget.isLoading) return;
    if (widget.controller.text.length == 6 && isKey) {
      widget.onSubmitted(widget.controller.text);
      setState(() {});
      return;
    }
    widget.onChanged(keys, isKey);
    debugPrint('keys pressed length ${widget.controller.text.length}');
    setState(() {});

    if (widget.controller.text.length == 6 && isKey) {
      widget.onSubmitted(widget.controller.text);
      setState(() {});
      return;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      decoration: const BoxDecoration(
        color: Color(0xFFF9F9F9),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Center(
              child: _buildNumericKeypad(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNumericKeypad() {
    final screenWidth = MediaQuery.of(context).size.width;

    final cardWidth = screenWidth / 3 - 20;
    final cardHeight = MediaQuery.of(context).size.height * 0.07;
    return
     Wrap(
      spacing: 10,
      runSpacing: 10,
      children: [
        SizedBox(
          width: cardWidth,
          height: cardHeight,
          child: _buildKeys(context: context, keys: '1'),
        ),
        SizedBox(
          width: cardWidth,
          height: cardHeight,
          child: _buildKeys(
            context: context,
            keys: '2',
          ),
        ),
        SizedBox(
          width: cardWidth,
          height: cardHeight,
          child: _buildKeys(
            context: context,
            keys: '3',
          ),
        ),
        SizedBox(
          width: cardWidth,
          height: cardHeight,
          child: _buildKeys(
            context: context,
            keys: '4',
          ),
        ),
        SizedBox(
          width: cardWidth,
          height: cardHeight,
          child: _buildKeys(
            context: context,
            keys: '5',
          ),
        ),
        SizedBox(
          width: cardWidth,
          height: cardHeight,
          child: _buildKeys(
            context: context,
            keys: '6',
          ),
        ),
        SizedBox(
          width: cardWidth,
          height: cardHeight,
          child: _buildKeys(
            context: context,
            keys: '7',
          ),
        ),
        SizedBox(
          width: cardWidth,
          height: cardHeight,
          child: _buildKeys(
            context: context,
            keys: '8',
          ),
        ),
        SizedBox(
          width: cardWidth,
          height: cardHeight,
          child: _buildKeys(
            context: context,
            keys: '9',
          ),
        ),
        SizedBox(
          width: cardWidth,
          height: cardHeight,
          child: Material(
            color: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(32),
            ),
            child: InkWell(
        borderRadius: BorderRadius.circular(32),

              onTap: () {
                _onKeyPressed('', isKey: false);
              },
              child: Center(
                child: Image.asset(
                  MediaRes.backSpaceKey,
                  width: 28,
                ),
              ),
            ),
          ),
        ),
        SizedBox(
          width: cardWidth,
          height: cardHeight,
          child: _buildKeys(
            context: context,
            keys: '0',
          ),
        ),
        SizedBox(
          width: cardWidth,
          height: cardHeight,
          child: Material(
            color: Theme.of(context).primaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(32),
            ),
            child: InkWell(
        borderRadius: BorderRadius.circular(32),

              onTap: () {
                _onKeyPressed('', isKey: false);
              },
              child: Center(
                child: widget.isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 3,
                        ),
                      )
                    : Image.asset(MediaRes.forwardKey, width: 24),
              ),
            ),
          ),
        ),
      ],
    );
  
  }

  Widget _buildKeys({
    required BuildContext context,
    required String keys,
  }) {
    return Material(
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(32),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(32),

        onTap: () {
          _onKeyPressed(keys);
        },

        child: Center(
          child: Text(
            keys,
            style: GoogleFonts.outfit(
              fontSize: 24.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

}
 