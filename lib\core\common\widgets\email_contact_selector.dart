import 'package:flutter/material.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:google_fonts/google_fonts.dart';

class EmailContactSelector extends StatefulWidget {
  final Function(String email, String name) onContactSelected;

  const EmailContactSelector({
    super.key,
    required this.onContactSelected,
  });

  @override
  State<EmailContactSelector> createState() => _EmailContactSelectorState();
}

class _EmailContactSelectorState extends State<EmailContactSelector> {
  List<Contact> _contacts = [];
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadContacts();
  }

  Future<void> _loadContacts() async {
    try {
      final contacts = await FlutterContacts.getContacts(
        withProperties: true,
        withPhoto: false,
      );
      
      if (!mounted) return;
      
      setState(() {
        _contacts = contacts.where((contact) {
          return contact.emails.isNotEmpty;
        }).toList();
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<Contact> get _filteredContacts {
    if (_searchQuery.isEmpty) return _contacts;
    
    return _contacts.where((contact) {
      final name = contact.displayName.toLowerCase();
      final email = contact.emails.first.address.toLowerCase();
      final query = _searchQuery.toLowerCase();
      
      return name.contains(query) || email.contains(query);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Text(
                'Select Contact',
                style: GoogleFonts.outfit(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                onChanged: (value) => setState(() => _searchQuery = value),
                decoration: InputDecoration(
                  hintText: 'Search contacts...',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
              ),
            ],
          ),
        ),
        const Divider(),
        if (_isLoading)
          const Expanded(
            child: Center(
              child: CircularProgressIndicator(),
            ),
          )
        else if (_contacts.isEmpty)
          Expanded(
            child: Center(
              child: Text(
                'No contacts with email found',
                style: GoogleFonts.outfit(
                  color: Colors.grey,
                ),
              ),
            ),
          )
        else
          Expanded(
            child: ListView.builder(
              itemCount: _filteredContacts.length,
              itemBuilder: (context, index) {
                final contact = _filteredContacts[index];
                final email = contact.emails.first.address;

                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: Theme.of(context).primaryColor,
                    child: Text(
                      contact.displayName.substring(0, 1).toUpperCase(),
                      style: GoogleFonts.outfit(color: Colors.white),
                    ),
                  ),
                  title: Text(
                    contact.displayName,
                    style: GoogleFonts.outfit(),
                  ),
                  subtitle: Text(
                    email,
                    style: GoogleFonts.outfit(color: Colors.grey),
                  ),
                  onTap: () => widget.onContactSelected(email, contact.displayName),
                );
              },
            ),
          ),
      ],
    );
  }
} 