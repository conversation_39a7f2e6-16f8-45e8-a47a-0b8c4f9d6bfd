import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:toastification/toastification.dart';

/// @isError _-> true mean -- we are displaying error message by default. incase the toast message is success then isError should be false
ToastificationItem? _currentToast;
void CustomToastification(
  BuildContext context, {
  required String message,
  bool isError = true,
  String successTitle = 'Success',
  String errorTitle = 'Error',
}) {
  if (_currentToast != null) {
    toastification.dismiss(_currentToast!);
    _currentToast = null; // Reset the reference after dismissing
  }

  // Show the new toast and save its reference
  _currentToast = toastification.show(
    context: context,
    backgroundColor: isError ? Colors.black : const Color(0xFFDDF7E0),
    foregroundColor: Colors.white,
    primaryColor: isError ? Colors.red : Theme.of(context).primaryColor,
    showProgressBar: false,
    closeOnClick: true,
    type: isError ? ToastificationType.error : ToastificationType.success,
    style: ToastificationStyle.flat,
    title: Text(
      isError ? errorTitle : successTitle,
      style: GoogleFonts.outfit(
        color: isError ? Colors.red : Colors.black,
        fontSize: 14,
        fontWeight: FontWeight.bold,
      ),
    ),
    description: Text(
      message,
      style: TextStyle(
        color: isError ? Colors.white : Colors.black.withOpacity(0.4),
        fontSize: 12,
        fontFamily: 'Avenir',
        fontWeight: FontWeight.w400,
      ),
    ),
    alignment: Alignment.topLeft,
    autoCloseDuration: const Duration(seconds: 5),
    closeButtonShowType: CloseButtonShowType.none,
  );
}
