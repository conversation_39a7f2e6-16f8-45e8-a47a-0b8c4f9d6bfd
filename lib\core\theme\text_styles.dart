import 'package:cbrs/core/utils/text_scaling.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTextStyles {
  static TextStyle heading1(BuildContext context) => GoogleFonts.outfit(
    fontSize: TextScaling.getResponsiveSize(context, 28),
    fontWeight: FontWeight.bold,
    height: 1.2,
  );

  static TextStyle heading2(BuildContext context) => GoogleFonts.outfit(
    fontSize: TextScaling.getResponsiveSize(context, 24),
    fontWeight: FontWeight.w600,
    height: 1.3,
  );

  static TextStyle body(BuildContext context) => GoogleFonts.outfit(
    fontSize: TextScaling.getResponsiveSize(context, 16),
    height: 1.5,
  );

  static TextStyle label(BuildContext context) => GoogleFonts.outfit(
    fontSize: TextScaling.getResponsiveSize(context, 14),
    color: Colors.black54,
    height: 1.4,
  );
}