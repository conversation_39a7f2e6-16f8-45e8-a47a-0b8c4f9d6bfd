import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/identity_verification/presentation/widgets/identity_verification_status_indicator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class IdPhotoInstructionsPage extends StatelessWidget {
  const IdPhotoInstructionsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: Column(
          children: [
            // Status indicator
            const IdentityVerificationStatusIndicator(
              currentStep: IdentityVerificationStep.instructions,
            ),
            Expanded(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 24.w),
                child: Column(
                  children: [
                    SizedBox(height: 40.h),

                    // ID Card Illustration
                    Container(
                      width: 300.w,
                      height: 200.h,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16.r),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(16.r),
                        child: SvgPicture.asset(
                          'assets/vectors/identity_hero.svg',
                          width: 280.w,
                          height: 180.h,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),

                    SizedBox(height: 60.h),

                    // Instructions text
                    Text(
                      'Before take your ID and Photo, please make sure that',
                      style: GoogleFonts.outfit(
                        fontSize: 20.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                        height: 1.3,
                      ),
                      textAlign: TextAlign.left,
                    ),

                    SizedBox(height: 32.h),

                    // Bullet points
                    Column(
                      children: [
                        _buildBulletPoint("Your ID isn't expired"),
                        SizedBox(height: 16.h),
                        _buildBulletPoint("Take a clear photo"),
                        SizedBox(height: 16.h),
                        _buildBulletPoint("Capture you entire ID"),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            // Take Photo button
            Padding(
              padding: EdgeInsets.fromLTRB(24.w, 0, 24.w, 24.h),
              child: CustomRoundedBtn(
                btnText: 'Take Photo',
                onTap: () {
                  context.pushNamed(AppRouteName.faceScanCamera);
                },
                isLoading: false,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBulletPoint(String text) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 6.w,
          height: 6.w,
          margin: EdgeInsets.only(top: 8.h, right: 12.w),
          decoration: const BoxDecoration(
            color: Colors.black,
            shape: BoxShape.circle,
          ),
        ),
        Expanded(
          child: Text(
            text,
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              fontWeight: FontWeight.w400,
              color: Colors.black,
              height: 1.4,
            ),
          ),
        ),
      ],
    );
  }
}
