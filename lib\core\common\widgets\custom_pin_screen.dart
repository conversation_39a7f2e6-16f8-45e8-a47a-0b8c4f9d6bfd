import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:pinput/pinput.dart';

class CustomPinScreen extends StatefulWidget {
  const CustomPinScreen({
    required this.controller,
    required this.onChanged,
    required this.isLoading,
    this.alwaysShowETBColor = false,
    required this.onSubmitted,
    super.key,
  });

  final TextEditingController controller;
  final void Function(String, bool) onChanged;
  final void Function(String) onSubmitted;

  final bool isLoading;
  final bool alwaysShowETBColor;

  @override
  State<CustomPinScreen> createState() => _CustomPinScreenState();
}

class _CustomPinScreenState extends State<CustomPinScreen> {
  bool _obscurePin = true;

  void handleShowPin() {
    setState(() {
      _obscurePin = !_obscurePin;
    });
  }

  void _onKeyPressed(String keys, {bool isKey = true}) {
    if (widget.controller.length == 6 && isKey) {
      widget.onSubmitted(widget.controller.text);
      setState(() {});
      return;
    }
    widget.onChanged(keys, isKey);
    debugPrint('keys pressed length ${widget.controller.length}');
    setState(() {});
  }

  final defaultPinTheme = PinTheme(
    width: 56,
    height: 56,
    textStyle: const TextStyle(
      fontSize: 20,
      color: Colors.black,
      fontWeight: FontWeight.w600,
    ),
    decoration: BoxDecoration(
      color: const Color(0xFF2C2B34).withOpacity(0.04),
      borderRadius: BorderRadius.circular(12),
    ),
  );

  Widget _buildPinDisplay() {
    return Pinput(
      length: 6,
      controller: widget.controller,
      defaultPinTheme: defaultPinTheme,
      focusedPinTheme: defaultPinTheme,
      submittedPinTheme: defaultPinTheme,
      obscureText: _obscurePin,
      obscuringCharacter: '*',
      useNativeKeyboard: false,
      onChanged: (value) {
        // if (value.length == 6) widget.onSubmitted(value);
      },
      onCompleted: (pinCode) {
        widget.onSubmitted(pinCode);

        print('Pin entered: $pinCode');
      },
    );
  }

  Widget _buildNumericKeypad() {
    final screenWidth = MediaQuery.of(context).size.width;

    final cardWidth = screenWidth / 3 - 20;
    return Wrap(
      spacing: 10,
      runSpacing: 10,
      children: [
        SizedBox(
          width: cardWidth,
          height: 64,
          child: _buildKeys(context: context, keys: '1'),
        ),
        SizedBox(
          width: cardWidth,
          height: 64,
          child: _buildKeys(
            context: context,
            keys: '2',
          ),
        ),
        SizedBox(
          width: cardWidth,
          height: 64,
          child: _buildKeys(
            context: context,
            keys: '3',
          ),
        ),
        SizedBox(
          width: cardWidth,
          height: 64,
          child: _buildKeys(
            context: context,
            keys: '4',
          ),
        ),
        SizedBox(
          width: cardWidth,
          height: 64,
          child: _buildKeys(
            context: context,
            keys: '5',
          ),
        ),
        SizedBox(
          width: cardWidth,
          height: 64,
          child: _buildKeys(
            context: context,
            keys: '6',
          ),
        ),
        SizedBox(
          width: cardWidth,
          height: 64,
          child: _buildKeys(
            context: context,
            keys: '7',
          ),
        ),
        SizedBox(
          width: cardWidth,
          height: 64,
          child: _buildKeys(
            context: context,
            keys: '8',
          ),
        ),
        SizedBox(
          width: cardWidth,
          height: 64,
          child: _buildKeys(
            context: context,
            keys: '9',
          ),
        ),
        SizedBox(
          width: cardWidth,
          height: 64,
          child: Material(
            color: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(32),
            ),
            child: InkWell(
              onTap: () {
                _onKeyPressed('', isKey: false);
              },
              child: Center(
                child: Image.asset(
                  MediaRes.backSpaceKey,
                  width: 28,
                ),
              ),
            ),
          ),
        ),
        SizedBox(
          width: cardWidth,
          height: 64,
          child: _buildKeys(
            context: context,
            keys: '0',
          ),
        ),
        SizedBox(
          width: cardWidth,
          height: 64,
          child: Material(
            color: widget.alwaysShowETBColor ? LightModeTheme().primaryColorBirr :Theme.of(context).primaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(32),
            ),
            child: InkWell(
              onTap: () {
                _onKeyPressed('', isKey: false);
              },
              child: Center(
                child: widget.isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 3,
                        ),
                      )
                    : Image.asset(MediaRes.forwardKey, width: 24),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildKeys({
    required BuildContext context,
    required String keys,
  }) {
    return Material(
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(32),
      ),
      child: InkWell(
        onTap: () {
          _onKeyPressed(keys);
        },
        borderRadius: BorderRadius.circular(32),
        child: Center(
          child: Text(
            keys,
            style: GoogleFonts.outfit(
              fontSize: 24.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.only(
          top: 12.h,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(24.r)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
          Container(
            width: 35,
            height: 2.5,
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(3),
            ),
          ),
          SizedBox(
            height: 24.h,
          ),
          Text(
            'Enter PIN to Verify Transfer',
            style: GoogleFonts.outfit(
              fontSize: 20.sp,
              fontWeight: FontWeight.w700,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Please enter your PIN to verify this transfer.',
            style: GoogleFonts.outfit(
              fontSize: 14.sp,
              color: Colors.black.withOpacity(0.3),
            ),
          ),
          SizedBox(height: 32.h),
          _buildPinDisplay(),
          SizedBox(height: 36.h),

          InkWell(
            onTap: handleShowPin,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  _obscurePin ? 'Show PIN' : 'Hide PIN',
                  style: GoogleFonts.outfit(
                    fontSize: 16.sp,
                    color: widget.alwaysShowETBColor ? LightModeTheme().primaryColorBirr : Theme.of(context).primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(
                  width: 8,
                ),
                Image.asset(
                  _obscurePin ? MediaRes.eyeOpen : MediaRes.eyeClose,
                  color: widget.alwaysShowETBColor ? LightModeTheme().primaryColorBirr : Theme.of(context).primaryColor,
                  width: 20.h,
                  height: 20.h,
                ),
              ],
            ),
          ),

          SizedBox(height: 24.h),
          Container(
            padding: const EdgeInsets.symmetric(vertical: 16),
            decoration: const BoxDecoration(
              color: Color(0xFFF9F9F9),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Center(
                    child: _buildNumericKeypad(),
                  ),
                ),
              ],
            ),
          ),

          Container(
            padding: const EdgeInsets.only(top: 16),
            decoration: const BoxDecoration(
              color: Color(0xFFF9F9F9),
            ),
          ),
          // _buildNumericKeypad(),
        ],
      ),
    )
    );
  }
}
