import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomRoundedBtn extends StatelessWidget {
  const CustomRoundedBtn({
    required this.btnText,
    required this.isLoading,
    super.key,
    this.onTap,
    this.borderRadius = 32,
    this.bgColor,
    this.textColor,
    this.isBtnActive = true,
    this.borderSide,
    this.loaderColor,
  });

  final void Function()? onTap;
  final String btnText;
  final bool isLoading;
  final double borderRadius;
  final Color? bgColor;
  final Color? textColor;
  final Color? loaderColor;

  final bool isBtnActive;
  final BorderSide? borderSide;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Center(
            child: GestureDetector(
              onTap: () {
                debugPrint('tap 11');
                if (isBtnActive) {
                  debugPrint('tap -----------r');

                  if (onTap != null) onTap!();
                }
              },

              //  onTap,
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 14.h),
                decoration: ShapeDecoration(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(borderRadius),
                    side: borderSide ?? BorderSide.none,
                  ),
                  color: isBtnActive
                      ? bgColor ?? Theme.of(context).primaryColor
                      : const Color(0xFFACACAC),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (isLoading) ...[
                      SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          color: loaderColor ?? Colors.white,
                          strokeWidth: 3,
                        ),
                      ),
                      const SizedBox(
                        width: 8,
                      ),
                    ],
                    Text(
                      btnText,
                      style: GoogleFonts.outfit(
                        fontWeight: FontWeight.w600,
                        color: textColor ?? Colors.white,
                        fontSize: 15.sp,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
