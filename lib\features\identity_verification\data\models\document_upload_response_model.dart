import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/identity_verification/domain/entities/document_upload_response.dart';

class DocumentUploadResponseModel extends DocumentUploadResponse {
  // Factory constructors for common scenarios
  factory DocumentUploadResponseModel.success({
    required String message,
    String? documentId,
    String? uploadId,
    String? status,
    Duration? estimatedProcessingTime,
    List<String>? nextSteps,
  }) {
    return DocumentUploadResponseModel(
      success: true,
      message: message,
      documentId: documentId,
      uploadId: uploadId,
      status: status,
      estimatedProcessingTime: estimatedProcessingTime,
      nextSteps: nextSteps,
    );
  }
  factory DocumentUploadResponseModel.failure({
    required String message,
    List<String>? errors,
  }) {
    return DocumentUploadResponseModel(
      success: false,
      message: message,
      errors: errors,
    );
  }
  const DocumentUploadResponseModel({
    required super.success,
    required super.message,
    super.documentId,
    super.uploadId,
    super.status,
    super.estimatedProcessingTime,
    super.nextSteps,
    super.errors,
  });

  factory DocumentUploadResponseModel.fromJson(Map<String, dynamic> json) {
    // Parse estimated processing time from minutes to Duration
    Duration? processingTime;
    final estimatedMinutes =
        AppMapper.safeInt(json['estimatedProcessingTimeMinutes']);
    if (estimatedMinutes > 0) {
      processingTime = Duration(minutes: estimatedMinutes);
    }

    // Parse next steps array
    List<String>? nextSteps;
    final nextStepsData = json['nextSteps'];
    if (nextStepsData is List) {
      nextSteps = nextStepsData.map((step) => step.toString()).toList();
    }

    // Parse errors array
    List<String>? errors;
    final errorsData = json['errors'];
    if (errorsData is List) {
      errors = errorsData.map((error) => error.toString()).toList();
    }

    return DocumentUploadResponseModel(
      success: AppMapper.safeBool(json['success']),
      message: AppMapper.safeString(json['message']),
      documentId: AppMapper.safeString(json['documentId']),
      uploadId: AppMapper.safeString(json['uploadId']),
      status: AppMapper.safeString(json['status']),
      estimatedProcessingTime: processingTime,
      nextSteps: nextSteps,
      errors: errors,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'documentId': documentId,
      'uploadId': uploadId,
      'status': status,
      'estimatedProcessingTimeMinutes': estimatedProcessingTime?.inMinutes,
      'nextSteps': nextSteps,
      'errors': errors,
    };
  }

  DocumentUploadResponseModel copyWith({
    bool? success,
    String? message,
    String? documentId,
    String? uploadId,
    String? status,
    Duration? estimatedProcessingTime,
    List<String>? nextSteps,
    List<String>? errors,
  }) {
    return DocumentUploadResponseModel(
      success: success ?? this.success,
      message: message ?? this.message,
      documentId: documentId ?? this.documentId,
      uploadId: uploadId ?? this.uploadId,
      status: status ?? this.status,
      estimatedProcessingTime:
          estimatedProcessingTime ?? this.estimatedProcessingTime,
      nextSteps: nextSteps ?? this.nextSteps,
      errors: errors ?? this.errors,
    );
  }
}
