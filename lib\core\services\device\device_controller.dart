import 'package:cbrs/core/services/connectivity/connectivity_controller.dart';
import 'package:cbrs/core/services/navigation/navigation_service.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/auth/data/models/device_check_response.dart';
import 'package:cbrs/features/auth/data/repositories/device_check_repository.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';

class DeviceCheckController extends GetxController {
  DeviceCheckController({
    required DeviceCheckRepository deviceCheckRepository,
    required NavigationService navigationService,
  })  : _deviceCheckRepository = deviceCheckRepository,
        _navigationService = navigationService;
  static const String _pinKey = 'stored_pin';
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();

  final DeviceCheckRepository _deviceCheckRepository;
  final NavigationService _navigationService;
  final _deviceVerified = false.obs;
  final _isInitialized = false.obs;
  final _noConnectivity = false.obs;

  final Rx<DeviceCheckData?> _deviceData = Rx<DeviceCheckData?>(null);
  DeviceCheckData? get deviceData => _deviceData.value;
  Future<void> reSetDeviceData(DeviceCheckData? value) async {
    _deviceData.value = value;
  }

  bool get isDeviceVerified => _deviceVerified.value;
  bool get isInitialized => _isInitialized.value;
  bool get noConnectivity => _noConnectivity.value;

  Future<void> checkDeviceOnStartup() async {
    debugPrint('is Initialised value 🇨🇦 ');
    if (_isInitialized.value) return;

    try {
      debugPrint('\n=== Device Check Started🏾🇧🇲  🌽===');

      final connectivityController = Get.find<ConnectivityController>();
      final hasConnection = await connectivityController.hasRealConnectivity();

      if (!hasConnection) {
        debugPrint(
          'No real connectivity detected - Redirecting to ConnectionLost',
        );
        _noConnectivity.value = true;
        await _navigationService.handleRedirect(AppRouteName.connectionLost);
        return;
      }

      final response = await _deviceCheckRepository.checkDevice();
      _deviceData.value = response.data;
      _deviceVerified.value = response.success && response.data != null;

      if (!_deviceVerified.value) {
        debugPrint('Device not verified - Redirecting to SignIn');
        await _navigationService.handleRedirect(AppRouteName.signIn);
      } else if (response.data == null) {
        debugPrint(
          'Device verified but no data - Redirecting to TokenDeviceLogin',
        );
        await _navigationService.handleRedirect(AppRouteName.tokenDeviceLogin);
      }
    } on DioException catch (e) {
      _deviceVerified.value = false;
      debugPrint('\n=== Device Check DioException ===');
      debugPrint('Error Type: ${e.type}');
      debugPrint('Error Message: ${e.message}');
      debugPrint('Response: ${e.response?.data}');

      if (_isNetworkError(e) || await _isWeakConnection()) {
        debugPrint(
          'Network/Weak connection error - Redirecting to ConnectionLost',
        );
        _noConnectivity.value = true;
        await _navigationService.handleRedirect(AppRouteName.connectionLost);
      } else {
        debugPrint('Non-network error - Redirecting to SignIn');
        await _navigationService.handleRedirect(AppRouteName.signIn);
      }
    } catch (e) {
      debugPrint('\n=== Device Check Error ===');
      debugPrint('Error Type: ${e.runtimeType}');
      debugPrint('Error Details: $e');
      _deviceVerified.value = false;

      if (e.toString().toLowerCase().contains('network') ||
          e.toString().toLowerCase().contains('connection')) {
        debugPrint('Network-related error - Redirecting to ConnectionLost');
        _noConnectivity.value = true;
        await _navigationService.handleRedirect(AppRouteName.connectionLost);
      } else {
        debugPrint('General error - Redirecting to SignIn');
        await _navigationService.handleRedirect(AppRouteName.connectionLost);
      }
    } finally {
      _isInitialized.value = true;
      debugPrint('Device Check Initialization Complete');
      debugPrint('=== Device Check End ===\n');
    }
  }

  Future<bool> _isWeakConnection() async {
    try {
      final connectivityController = Get.find<ConnectivityController>();
      return !(await connectivityController.hasRealConnectivity());
    } catch (e) {
      debugPrint('Error checking weak connection: $e');
      return true; // Assume weak connection on error
    }
  }

  bool _isNetworkError(DioException e) {
    return e.type == DioExceptionType.connectionTimeout ||
        e.type == DioExceptionType.sendTimeout ||
        e.type == DioExceptionType.receiveTimeout ||
        e.type == DioExceptionType.connectionError ||
        e.type == DioExceptionType.unknown ||
        e.message?.toLowerCase().contains('network') == true ||
        e.message?.toLowerCase().contains('connection') == true ||
        e.message?.toLowerCase().contains('timeout') == true;
  }

  void resetInitialization() {
    _isInitialized.value = false;
    _deviceVerified.value = false;
    _noConnectivity.value = false;
  }

  Future<void> storePin(String pin) async {
    await _secureStorage.write(key: _pinKey, value: pin);
  }

  Future<String?> getStoredPin() async {
    return _secureStorage.read(key: _pinKey);
  }

  Future<void> clearStoredPin() async {
    await _secureStorage.delete(key: _pinKey);
  }
}
