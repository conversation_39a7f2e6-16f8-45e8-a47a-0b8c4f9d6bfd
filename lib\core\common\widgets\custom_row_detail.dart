import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:googleapis/gmail/v1.dart';

class CustomRowDetail extends StatelessWidget {
  final String label, value;
  final bool hasBorder;
  final Color labelColor, valueColor;
  const CustomRowDetail({
    super.key,
    required this.label,
    required this.value,
    this.hasBorder = true,
    this.labelColor = const Color(0xFF000000),
    this.valueColor = const Color(0xFF000000),
  });

  @override
  Widget build(BuildContext context) {
    return _buildDetailItem();
  }

  Widget _buildDetailItem() {
    return Row(
      children: [
        Expanded(
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    label,
                    style: GoogleFonts.outfit(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                        color: labelColor),
                  ),
                  CustomBuildText(
                    text: value,
                    fontSize: 14.sp,
                    color: valueColor,
                    fontWeight: FontWeight.w600,
                  ),
                ],
              ),
              SizedBox(height: 8.h),
              if (hasBorder)
                Row(
                  children: [
                    Expanded(
                        child:
                            Divider(color: Color(0xFF2C2B34).withOpacity(0.2))),
                  ],
                ),
              if (hasBorder) SizedBox(height: 10.h),
            ],
          ),
        ),
      ],
    );
  }
}
