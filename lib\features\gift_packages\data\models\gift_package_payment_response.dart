class GiftPackagePaymentResponse {
  final int statusCode;
  final bool success;
  final GiftPackagePaymentData data;
  final int? otpCode;
  final String message;

  GiftPackagePaymentResponse({
    required this.statusCode,
    required this.success,
    required this.data,
    this.otpCode,
    required this.message,
  });

  factory GiftPackagePaymentResponse.fromJson(Map<String, dynamic> json) {
    return GiftPackagePaymentResponse(
      statusCode: json['statusCode'] as int,
      success: json['success'] as bool,
      data: GiftPackagePaymentData.fromJson(json['data'] as Map<String, dynamic>),
      otpCode: json['otpCode'] as int?,
      message: json['message'] as String,
    );
  }
}

class GiftPackagePaymentData {
  final String senderId;
  final String senderName;
  final String senderPhone;
  final String? senderEmail;
  final String transactionOwner;
  final String beneficiaryId;
  final String beneficiaryName;
  final String beneficiaryPhone;
  final String? senderConnectCode;
  final String? beneficiaryConnectCode;
  final String transactionType;
  final String giftPackageId;
  final String merchantId;
  final String merchantTill;
  final String merchantType;
  final int totalGiftPackageQty;
  final String? packageDiscountLevel;
  final String? packageDiscountId;
  final String? packageCampaignId;
  final double billAmount;
  final String? originalCurrency;
  final double? serviceCharge;
  final double? VAT;
  final double totalAmount;
  final double? exchangeRate;
  final String billRefNo;
  final String billReason;
  final String? authorization_type;
  final String status;
  final String createdAt;
  final String lastModified;
  final String? id;
  final double? paidAmount;
  final bool? overPaid;
  final bool? underPaid;
  final bool? isExpired;

  GiftPackagePaymentData({
    required this.senderId,
    required this.senderName,
    required this.senderPhone,
    this.senderEmail,
    required this.transactionOwner,
    required this.beneficiaryId,
    required this.beneficiaryName,
    required this.beneficiaryPhone,
    this.senderConnectCode,
    this.beneficiaryConnectCode,
    required this.transactionType,
    required this.giftPackageId,
    required this.merchantId,
    required this.merchantTill,
    required this.merchantType,
    required this.totalGiftPackageQty,
    this.packageDiscountLevel,
    this.packageDiscountId,
    this.packageCampaignId,
    required this.billAmount,
    this.originalCurrency,
    this.serviceCharge,
    this.VAT,
    required this.totalAmount,
    this.exchangeRate,
    required this.billRefNo,
    required this.billReason,
    this.authorization_type,
    required this.status,
    required this.createdAt,
    required this.lastModified,
    this.id,
    this.paidAmount,
    this.overPaid,
    this.underPaid,
    this.isExpired,
  });

  factory GiftPackagePaymentData.fromJson(Map<String, dynamic> json) {
    return GiftPackagePaymentData(
      senderId: json['senderId'] as String? ?? '',
      senderName: json['senderName'] as String? ?? '',
      senderPhone: json['senderPhone'] as String? ?? '',
      senderEmail: json['senderEmail'] as String?,
      transactionOwner: json['transactionOwner'] as String? ?? '',
      beneficiaryId: json['beneficiaryId'] as String? ?? '',
      beneficiaryName: json['beneficiaryName'] as String? ?? '',
      beneficiaryPhone: json['beneficiaryPhone'] as String? ?? '',
      senderConnectCode: json['senderConnectCode'] as String?,
      beneficiaryConnectCode: json['beneficiaryConnectCode'] as String?,
      transactionType: json['transactionType'] as String? ?? '',
      giftPackageId: json['giftPackageId'] as String? ?? '',
      merchantId: json['merchantId'] as String? ?? '',
      merchantTill: json['merchantTill'] as String? ?? '',
      merchantType: json['merchantType'] as String? ?? '',
      totalGiftPackageQty: json['totalGiftPackageQty'] as int? ?? 0,
      packageDiscountLevel: json['packageDiscountLevel'] as String?,
      packageDiscountId: json['packageDiscountId'] as String?,
      packageCampaignId: json['packageCampaignId'] as String?,
      billAmount: (json['billAmount'] as num?)?.toDouble() ?? 0.0,
      originalCurrency: json['originalCurrency'] as String?,
      serviceCharge: (json['serviceCharge'] as num?)?.toDouble() ?? 0.0,
      VAT: (json['VAT'] as num?)?.toDouble() ?? 0.0,
      totalAmount: (json['totalAmount'] as num?)?.toDouble() ?? 0.0,
      exchangeRate: (json['exchangeRate'] as num?)?.toDouble(),
      billRefNo: json['billRefNo'] as String? ?? '',
      billReason: json['billReason'] as String? ?? '',
      authorization_type: json['authorization_type'] as String?,
      status: json['status'] as String? ?? '',
      createdAt: json['createdAt'] as String? ?? '',
      lastModified: json['lastModified'] as String? ?? '',
      id: json['id'] as String?,
      paidAmount: (json['paidAmount'] as num?)?.toDouble(),
      overPaid: json['overPaid'] as bool?,
      underPaid: json['underPaid'] as bool?,
      isExpired: json['isExpired'] as bool?,
    );
  }
}
