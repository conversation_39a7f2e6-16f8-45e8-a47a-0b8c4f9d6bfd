import 'dart:io';

import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class HandleDownloadReciept {
  static Future<void> downloadReceipt(
    BuildContext context,
    String url,
  ) async {
    debugPrint('=== Download Receipt Debug ===');
    debugPrint('URL: $url');

    try {
      final uri = Uri.parse(url);
      debugPrint('URI parsed successfully: $uri');

      // For PDF files, we should use platform-specific handling
      if (Platform.isAndroid || Platform.isIOS) {
        final launched = await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );

        debugPrint('Launch attempt result: $launched');

        if (!launched) {
          CustomToastification(context, message: 'Could not open PDF viewer');
          return;
        }
      } else {
        // For web or other platforms
        final launched = await launchUrl(uri);
        debugPrint('Launch attempt result: $launched');

        if (!launched) {
          CustomToastification(
            context,
            message: 'Could not open download link',
          );
          return;
        }
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
      CustomToastification(context, message: 'Error opening download link');
      return;
    }
    debugPrint('=== End Download Receipt Debug ===');
  }
}
