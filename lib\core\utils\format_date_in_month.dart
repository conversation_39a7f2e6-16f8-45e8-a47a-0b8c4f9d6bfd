import 'package:intl/intl.dart';

String formatDateInMonth(String dateString, {bool isAbbreviate = false}) {
  try {
    DateTime parsedDate = DateTime.parse(dateString);
    return isAbbreviate
        ? DateFormat('MMM d, yyyy').format(parsedDate)
        : DateFormat('MMMM d, yyyy').format(parsedDate);
  } catch (e) {
    return 'Invalid date';
  }
}

String formatFriendlyDate(int leftDates) {
  String stringDate;

  if (leftDates > 1) {
    stringDate = '$leftDates Days ';
  } else if (leftDates == 1) {
    stringDate = '$leftDates Day';
  } else {
    stringDate = 'Today';
  }

  return stringDate;
}

// Final Day 