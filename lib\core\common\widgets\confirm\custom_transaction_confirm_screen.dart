import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/utils/get_app_theme_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

class CustomTransactionConfirmScreen extends StatelessWidget {
  const CustomTransactionConfirmScreen({
    required this.totalAmount,
    required this.child,
    this.customImage,
    this.isGuestFlow = false,
    this.isUsdTransfer = false,
    super.key,
    this.description = 'Confirm Transfer',
  });
  final double totalAmount;
  final Widget child;
  final bool isGuestFlow;
  final bool isUsdTransfer;

  final String description;

  final Widget? customImage;
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 24,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          customImage ??
              Image.asset(
                Get.find<GetAppThemeController>().isBirrTheme.value
                    ? MediaRes.confirmBirr3D
                    : MediaRes.confirmUsd3D,
                width: 140.w,
                height: 54.h,
              ),
          const SizedBox(
            height: 16,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: CustomBuildText(
                  text: (isGuestFlow || isUsdTransfer)
                      ? '${totalAmount.toStringAsFixed(2)} USD'
                      : '${totalAmount.toStringAsFixed(2)} ETB',

                  /* 
                 isUsdTransfer
                    ? Text(
                        '${totalAmount.toStringAsFixed(2)} USD',
                        textAlign: TextAlign.center,
                        style: GoogleFonts.outfit(
                          fontSize: 24.sp,
                          fontWeight: FontWeight.w700,
                          color: Colors.black,
                        ),
                      )
                    : Text(
                        isGuestFlow
                            ? '${totalAmount.toStringAsFixed(2)} USD'
                            : Get.find<GetAppThemeController>()
                                    .isBirrTheme
                                    .value
                                ? '${totalAmount.toStringAsFixed(2)} ETB'
                                : '\$${totalAmount.toStringAsFixed(2)}',
                      
                      */
                  textAlign: TextAlign.center,
                  style: GoogleFonts.outfit(
                    fontSize: 24.sp,
                    fontWeight: FontWeight.w800,
                    color: Colors.black,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 4.h),
          Text(
            description,
            style: GoogleFonts.outfit(
              fontSize: 16.sp,
              fontWeight: FontWeight.w400,
              color: Colors.black.withOpacity(0.3),
            ),
          ),
          SizedBox(height: 24.h),
          Row(
            children: [
              Text(
                'Transaction Details',
                style: GoogleFonts.outfit(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w700,
                  color: Colors.black,
                  // backgroundColor: Colors.red
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          child,
        ],
      ),
    );
  }
}
