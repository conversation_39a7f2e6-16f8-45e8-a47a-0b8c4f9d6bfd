import 'package:equatable/equatable.dart';

class AddMoneyResponse extends Equatable {
  final String senderId;
  final String senderName;
  final String senderPhone;
  final String senderEmail;
  final String transactionOwner;
  final String beneficiaryId;
  final String beneficiaryName;
  final String beneficiaryAccountNo;
  final String transactionType;
  final double billAmount;
  final String originalCurrency;
  final String billRefNo;
  final String billReason;
  final String authorizationType;
  final String status;
  final DateTime createdAt;
  final DateTime lastModified;
  final String id;
  final double serviceCharge;
  final double vat;
  final double totalAmount;
  final double paidAmount;
  final bool enabled;
  final bool isDeleted;
  final bool isExpired;

  const AddMoneyResponse({
    required this.senderId,
    required this.senderName,
    required this.senderPhone,
    required this.senderEmail,
    required this.transactionOwner,
    required this.beneficiaryId,
    required this.beneficiaryName,
    required this.beneficiaryAccountNo,
    required this.transactionType,
    required this.billAmount,
    required this.originalCurrency,
    required this.billRefNo,
    required this.billReason,
    required this.authorizationType,
    required this.status,
    required this.createdAt,
    required this.lastModified,
    required this.id,
    required this.serviceCharge,
    required this.vat,
    required this.totalAmount,
    required this.paidAmount,
    required this.enabled,
    required this.isDeleted,
    required this.isExpired,
  });

  @override
  List<Object?> get props => [
        senderId,
        senderName,
        senderPhone,
        senderEmail,
        transactionOwner,
        beneficiaryId,
        beneficiaryName,
        beneficiaryAccountNo,
        transactionType,
        billAmount,
        originalCurrency,
        billRefNo,
        billReason,
        authorizationType,
        status,
        createdAt,
        lastModified,
        id,
        serviceCharge,
        vat,
        totalAmount,
        paidAmount,
        enabled,
        isDeleted,
        isExpired,
      ];
}