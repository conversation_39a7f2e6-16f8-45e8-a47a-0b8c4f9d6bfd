import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

class IdentityVerificationCard extends StatelessWidget {
  final bool isUSD;

  const IdentityVerificationCard({
    Key? key,
    this.isUSD = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20.r),
        gradient: LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: isUSD
              ? [
                  const Color(0xFFE6F1FF), // Light blue for USD
                  const Color(0xFFE6F1FF).withOpacity(0.6),
                ]
              : [
                  const Color(0xFFE6F7E2), // Light green for non-USD
                  const Color(0xFFE6F7E2).withOpacity(0.6),
                ],
          stops: const [0.0, 0.4],
        ),
      ),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Background image with blend
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20.r),
              child: Opacity(
                opacity: 0.37,
                child: Image.asset(
                  MediaRes.identityBg,
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),

          // Content
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header text
                Text(
                  'Please verify your account',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w700,
                    color: Colors.grey[800],
                  ),
                ),
                SizedBox(height: 4.h),
                // Description text
                Text(
                  'To ensure the security of your account and protect against fraud, we require you to complete our identity verification process.',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.grey[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 12.h),
                // Just the button - icon is positioned separately
                SizedBox(
                  height: 50.h,
                  width: 130.w,
                  child: ElevatedButton(
                    onPressed: () {
                      context.pushNamed(AppRouteName.identityVerification);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                      elevation: 0, // No shadow
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(40.r),
                      ),
                      padding: EdgeInsets.symmetric(vertical: 10.h),
                    ),
                    child: Text(
                      'Get Verified',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Positioned icon that doesn't affect card height
          Positioned(
            right: 16.w,
            bottom: 0.h,
            child: Image.asset(
              MediaRes.verifyAccountIcon,
              width: 110.h,
              height: 110.h,
            ),
          ),
        ],
      ),
    );
  }
}
