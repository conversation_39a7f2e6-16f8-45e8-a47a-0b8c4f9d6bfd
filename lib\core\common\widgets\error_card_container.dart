import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ErrorCardContainer extends StatelessWidget {
  final String errorTitle, errorMessage, iconUrl;
  final bool hasBottomButton;
  final VoidCallback? onTap;
  final Color? bgColor, titleColor, messageColor;
  const ErrorCardContainer({
    super.key,
    this.errorTitle = 'Payment Failed',
    this.errorMessage =
        'Unfortunately, the transaction could not be completed. Please try again or check your payment details.',
    this.hasBottomButton = true,
    this.onTap,
    this.iconUrl = 'assets/images/failure_image.png',
    this.bgColor,
    this.titleColor,
    this.messageColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          const Spacer(),
          Container(
            decoration: BoxDecoration(
              color: bgColor ?? Color(0xFFFFEDED),
              borderRadius: BorderRadius.circular(16),
            ),
            padding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 42.h),
            child: Column(
              children: [
                Image.asset(
                  iconUrl,
                  height: 140,
                  width: 140,
                  fit: BoxFit.cover,
                ),
                SizedBox(height: 4.h),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Center(
                        child: Text(
                          textAlign: TextAlign.center,
                          errorTitle,
                          style: GoogleFonts.outfit(
                            fontSize: 22.sp,
                            fontWeight: FontWeight.bold,
                            color: titleColor ?? Colors.black,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8.h),
                Text(
                  errorMessage,
                  textAlign: TextAlign.center,
                  style: GoogleFonts.outfit(
                    fontSize: 14,
                    color: messageColor ?? Color(0xFF595959),
                  ),
                ),
              ],
            ),
          ),
          const Spacer(),
          if (hasBottomButton)
            CustomButton(
              text: 'Back to Home',
              onPressed: () {
                onTap ?? context.go(AppRouteName.home);

                // WidgetsBinding.instance.addPostFrameCallback((_) {
                //   context.go('/main/main');
                // });
              },
              options: CustomButtonOptions(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 16),
                color: Theme.of(context).primaryColor,
                textStyle: GoogleFonts.outfit(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
                borderRadius: BorderRadius.circular(32),
              ),
            ),
          SizedBox(height: 16.h),
        ],
      ),
    );
  }
}
