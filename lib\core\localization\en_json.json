{"auth": {"signu_up": {"sign_up": "Sign Up", "description": "Create an account on CONNECT and make global money transfers simple and seamless.", "first_name": "First Name", "last_name": "Last Name", "middle_name": "Middle Name", "gender": "Gender", "male": "Male", "female": "Female", "phone_number": "Phone Number", "email_address": "Email Address", "country": "Country", "city": "City", "date_of_birth": "Date of Birth", "already_have": "Already have an account?", "login": "<PERSON><PERSON>", "country_list": "Country List", "search": "Search"}, "login": {"title": "<PERSON><PERSON>", "description": "Welcome Back! Log in to your CONNECT account to make transfers effortlessly.", "email_address": "Email Address", "email": "Email", "phone_number": "Phone Number", "login": "<PERSON><PERSON>", "dont_have": "Don't have an account?", "signup": "Sign Up"}, "email_verification": {"title": "Email Verification", "description": "We've sent a verification code to your email. Please enter the code to confirm your email address.", "sec": "Sec", "didnt_receive": "Didn’t receive code?", "recend_otp": "Resend OTP", "verify": "Verify"}, "phone_verification": {"title": "Phone Verification", "description": "We've sent a verification code to your phone number. Please enter the code to confirm your phone number.", "sec": "Sec", "didnt_receive": "Didn’t receive code?", "recend_otp": "Resend OTP", "verify": "Verify", "verify_with_whatsapp": "Verify with WhatsApp", "whatsapp_description": "Check your WhatsApp for the verification code and enter it in the box above."}, "create_pin": {"create_pin": "Create PIN", "description": "Create a new secure PIN and confirm it. Make sure to use a strong PIN.", "new_pin": "New PIN", "confrim_new_pin": "Confirm New PIN"}, "pin_login": {"welcome_back": "Welcome back!", "description": "Enter your PIN and sign in to your Nedaj account.", "show_pin": "Show PIN", "forgot_pin": "Forgot PIN"}, "forgot_pin": {"title": "Forgot PIN", "description": "To reset your PIN, please enter your email/phone number below and verify it first.", "email_address": "Email Address", "email": "Email", "phone_number": "Phone Number", "continue": "Continue"}}, "car_loan": {"car_loans": "Car Loans", "description": "Choose your preferred car, review the details, and apply for your car effortlessly.", "body_type": "Body Type(style)", "manual": "Manual", "seat": "<PERSON><PERSON>", "cc": "cc", "model": "Model", "car_loans_detail_page": {"car_loans": "Car Loans", "model": "Model", "body_type": "Body Type", "transmission": "Transmission", "max_speed": "Max Speed", "make": "Make", "color": "Color", "no_of_seats": "No of Seats", "drive_train": "drive_train", "engine_size": "Engine Size", "fuel_type": "Fuel Type", "mile_age": "MileAge", "fuel_efficiency": "Fuel Efficiency", "horse_power": "HorsePower", "manufactureYear": "Manufacture Year", "similar_cars": "Similar Cars", "apply_for_loans": "Apply for Loans"}, "car_loan_application_page": {"car_loans": "Car Loans", "select_bank": "Select Bank", "selected": "Selected", "loan_packages": "Loan Packages(up front payment", "loan_payment_info": "Loan Payment Information", "loan_payment_info_desc": "Laon information may vary based on your selected preferences", "upfront_payment_amount": "up front payment Amount", "loan_period": "Loan Period", "payment_amount": "Payment Amount", "interest_rate": "Interest Rate", "daily_penalty_fee": "Daily Penalty Fee", "facilitation_fee": "Facilitation Fee", "next": "Next"}, "car_loan_appliacation_doc_page": {"required_doc": "Required Documents", "required_doc_for": "Required Documents for Loan", "description": "Please prepare these documents; we will email you if further documents are needed.", "proof_of": {"proof_of_identity": "Proof of Identity ", "proof_of_identity_desc": "Please prepare a valid document, like your ID card, passport or driver's license.", "proof_of_residency": "Proof of Residence ", "proof_of_residency_desc": "Prepare your residential document to upload as part of the verification process.", "proof_of_income": "Income Proof ", "proof_of_income_desc": "Please prepare documents like your recent payslips, tax returns, or bank statements.", "proof_of_credity": " Credit Report ", "proof_of_credity_desc": "Please prepare your credit report to upload, as it helps for the loan application.", "continue": "Continue"}}, "car_laon_apply_terms": {"loan_terms": "Loan Terms & Conditions", "our_terms": "Our Terms & Condition", "description": "Read our terms and conditions carefully before your continue with your application.", "i_agree": "I agree and Continue"}, "confirm_loan_apply_page": {"loan_application": "Loan Application", "confirm_loan_appl": "Confirm Loan Application", "description": "Proceed to pay the application fee to finalize your loan request.", "wallet_balance": "Wallet Balance", "loan_application_fee": "Loan Application Fee", "transaction_details": "Transaction Details", "customer_name": "Customer Name", "loan_type": "Loan Type", "car_model": "Car Model", "loan_period": "Loan Period", "car_price": "Car Price", "down_payment": "Down Payment", "interest_rate": "Interest Rate", "application_fee": "Application Fee", "total": "Total"}, "success_car_loan_apply": {"success_appl": "Successfully Applied!", "description": "You have successfully applied for the loan and paid the application fee. We will contact you via email with the next steps to complete your loan process.", "loan_application_fee": "Loan Application Fee", "transaction_details": "Transaction Details", "customer_name": "Customer Name", "loan_type": "Loan Type", "car_model": "Car Model", "car_price": "Car Price", "loan_period": "Loan Period", "down_payment": "Down Payment", "payment_reference": "Payment Reference", "application_fee": "Application Fee", "application_date": "Application Date", "total": "Total", "back_to_loans": "Back To Loans", "info": "Info", "application_code": "Application Code", "application_desc": "This unique code is essential for tracking and processing your loan application. Please keep it secure and share it only with authorized personnel if required. You can use this code to check the status of your application or for any inquiries. Thank you for choosing us to assist with your financial needs!", "loan_application_code": "Loan Application Code", "copied": "<PERSON>pied"}}, "change_to_birr": {"change_to_birr": "Change To Birr", "description": "Enter the amount to load your Birr account and get it ready for sending to anyone in ETB.", "wallet_balance": "Wallet Balance", "continue": "Continue", "amount_in_etb": "Amount in ETB", "confirm_transfer_change_page": {"confirm_transfer_change": "Confirm Transfer Change", "description": "Review the amount and transaction details, then confirm the change.", "transaction_details": "Transaction Details", "exchange_rates": "Exchange rate", "date": "Date", "amount_in_usd": "Amount in USD", "amount_in_etb": "Amount in ETB", "total": "Total", "confirm": "Confirm"}, "success_transfer_change_page": {"successfully_sent": "<PERSON><PERSON><PERSON>et Loaded Successfully!", "successfully_desc": "Your Birr wallet has been successfully loaded! You're now ready to send money in Birr with ease!", "transaction_detail": {"transaction_details": "Transaction Details", "transfer_reference": "Transfer Reference", "transfer_date": "Transfer Date", "exchange_rates": "Exchange rate", "amount_in_usd": "Amount in USD", "amount_in_etb": "Amount in ETB", "total": "Total", "back_to_home": "Back to Home"}}}, "chat": {"chat": "Cha<PERSON>", "description": "Connect with your loved ones, share messages, and stay connected with ease.", "updating": "Updating", "search": "Search", "online": "Online", "chat_detail": {"start_conversation": "Start Conversation, Say Hi to", "block_user": "Block User", "block_desc": "Are you sure you want to block this user? This action can’t be un done.", "back": "Back", "block": "Block", "selected": "Selected", "delete_message": "Delete Message", "delete_desc": "Are you sure you want to delete this message? This action can’t be un done.", "also_delete": "Also Delete from", "delete": "Delete", "forward_to": "Forward to", "recipents": "<PERSON><PERSON><PERSON><PERSON>", "send_money": "Send Money (USD Wallet)", "request_money": "Request Money", "money_sent": "Money Sent", "request_sent": "Request Sent", "money_received": "Money Received", "details": "Details", "message": "Message", "cancel_request": "Cancel Request", "request_canceled": "Request Canceled", "rejected": "Rejected", "you_have_recieved": "You Recieved Gift"}, "chat_send_money": {"send_money": "Send Money (USD Wallet)", "add_amount": "Add Amount", "description": "Enter the amount you wish to send to the recipient and submit.", "wallet_balance": "Wallet Balance", "continue": "Continue"}, "confirm_send_money_page": {"confirm_transfer": "Confirm Transfer", "description": "Review the amount and transaction details, then confirm the transfer.", "transaction_details": "Transaction Details", "sender_name": "Sender Name", "recipent_name": "Recipent Name", "recipient_email": "Recipient Email", "recipient_phone_number": "Recipient Phone Number", "date": "Date", "amount_in_usd": "Amount in USD", "total": "Total", "confirm": "Confirm"}, "success_send_money_page": {"successfully_sent": "Successfully Sent", "successfully_desc": "Your transaction was completed successfully.", "transaction_detail": {"transaction_details": "Transaction Details", "sender_name": "Sender Name", "recipent_name": "Recipent Name", "recipent_email": "<PERSON><PERSON><PERSON>", "payment_reference": "Payment Reference", "date": "Date", "amount_in_usd": "Amount in USD", "total": "Total", "back_to_home": "Back to Home"}}, "chat_request_money": {"request_money": "Request Money", "add_amount": "Add Amount", "description": "Enter the amount you would like to request and confirm your request to proceed.", "wallet_balance": "Wallet Balance", "continue": "Continue"}, "confirm_request_money_page": {"request_money": "Request Money", "confirm_transfer": "Confirm Request", "description": "Review the amount and transaction details, then confirm the transfer.", "transaction_details": "Transaction Details", "requested_to": "Requested From", "recipent_name": "Recipent to", "date": "Date", "amount_in_usd": "Amount in USD", "total": "Total", "confirm": "Confirm"}, "no_chat_page": {"no_message": "No Messages", "get_started": "Get Started by messaging a friend"}, "contact_list": {"contact_list": "Contacts List", "description": "Select contacts to start a chat on Connect or invite them to join the app.", "contacts_on_connect": "Contacts on Connect", "last_seen_recently": "Last seen recently", "invite": "Invite"}, "chat_profile_detail": {"online": "Online", "profile_information": "Profile Information", "phone_number": "Phone Number", "email": "Email", "country": "Country", "city": "City", "date_of_birth": "Date of Birth", "update_profile": "Update Profile"}, "video_page": {"ringing": "Ringing", "gift_packages": "Gift Packages", "send": "Send", "select_gift_recipent": "Select Gift Recipent", "gift_recipent_desc": "You can select multiple users in the call and send them a gift.", "selected": "Selected", "send_gift": "Send Gift", "confirm_gift_purchase": {"Confirm Purchase": "Confirm Purchase", "transaction_detail": "Transaction Details", "customer_name": "Customer Name", "recipent_name": "Recipent Name", "recipent_email": "<PERSON><PERSON><PERSON>", "recipent_phone": "Recipent Phone", "package_name": "Package Name", "package_price": "Package price", "super_market": "Super Market", "amount": "Amount", "total": "Total", "payment_reference": "Payment Reference", "confirm": "Confirm"}, "success_gift_purchase": {"successfully": "Successfully", "successfully_purchased": "Successfully Purchased!", "successfully_desc": "You have successfully purchased a gift and the order code for the gift is provided below.", "copy_order_code": "Copy Order Code", "copied": "<PERSON>pied"}, "gift_in_coins": "Gift in Coins", "you_have_recieved": "You Recieved Gift", "confirm_gift_coin": {"confirm_gift": "Confirm Gift", "coin_conversat": "Coin Conversation", "coin_amount": "Coin Amount", "recipent_recieve": "<PERSON><PERSON><PERSON>", "platform_fee": "Platform Fee", "total": "Total", "confirm": "Confirm"}}, "request_wallet": {"request_details": "Request Details", "money_requested": "Money Requested", "reject": "Reject", "accept": "Accept"}, "accept_send_page": {"send_money": "Send Money", "add_amount": "Add Amount", "description": "Enter the amount you wish to send to the recipient and submit.", "wallet_balance": "Wallet Balance", "continue": "Continue"}}, "gift_packages": {"gift_packages": "Gift Packages", "description": "Choose from the avavilable packeg lista nd made a purchase and givr a gift by buying a kind for your beloved once.", "package_list": "Package Lists", "search": "search", "categories": "Categories", "all_items": "All Items", "search_packages": "Search Packages", "search_result": "Search Results", "discount_ends": {"discount_ends_on": "Discount ends on", "day": "Day", "hr": "HR", "min": "MIN", "sec": "Sec", "discounted_prod": "Discounted Products", "off": "OFF"}, "gift_package_detail": {"gift_packages": "Gift Packages", "description": "Description", "buy": "Buy"}, "no_gift_page": {"no_gift": "No Gift Yet", "no_geft_desc": "You haven’t sent or received any gifts so far. Once you send or receive a gift, it will show up here."}, "package_reciepent_page": {"gift_packages": "Gift Packages", "reciepent_details": "Recipent <PERSON>", "description": "Provide the recipient's name and phone number so that they can get notified and receive their gifts.", "recipient_name": "Recipient Name", "enter_recipient_name": "Enter Recipient Name", "phone_number": "Phone Number", "continue": "Continue", "checking": "Checking"}, "gift_confirm_purchase_page": {"gift_packages": "Gift Packages", "confirm_purchase": "Confirm Purchase", "description": "Complete payment to finalize your gift package purchase for your loved ones.", "transaction_details": "Transaction Details", "customer_name": "Customer Name", "recipent_name": "Recipent Name", "recipent_phone": "Recipent Phone", "package_name": "Package Name", "package_price": "Package price", "quantity": "Quantity", "super_market": "Super Market", "amount": "Amount", "total": "Total"}, "gift_success_purchase_page": {"successfully_purchased": "Successfully Purchased!", "description": "CYou have successfully purchased a gift and the order code for the gift is provided below.", "transaction_details": "Transaction Details", "customer_name": "Customer Name", "recipent_name": "Recipent Name", "recipent_phone": "Recipent Phone", "package_name": "Package Name", "package_price": "Package price", "quantity": "Quantity", "super_market": "Super Market", "payment_reference": "Payment Reference", "payment_date": "Payment Date", "amount": "Amount", "total": "Total", "back_to_packages": "Back to Packages", "info": "Info", "thank_you": "Thank You!", "thank_you_desc": "This unique code is essential for redeeming the gift. Please share it with the gift receiver, as they’ll need to provide this code to collect their order. They can pick up the gift at 'Shop Name,' available at any branch. Ensure the code is kept secure to avoid any inconvenience. Thank you for choosing us to send your thoughtful gift!", "copy_order_code": "Copy Order Code", "copied": "<PERSON>pied"}}, "guest": {"welcome_back": "Welcome Back!", "you_are_in": "You are in Guest Mode.", "description": " Create an account to access full features. For now, send money directly with your card.", "send_money": "Send Money", "send_money_desc": "Select bank and send money directly to your loved one's account using your card", "package": {"package_and_loans": "Package & Loans", "gift_package": "Gift Package", "gift_desc": "Browse Gift Packages, purchase, and share the code for easy gifting.", "car_loans": "Car Loans", "car_desc": "Browse our top car selection, apply for a loan, and drive away with ease.", "mortgage_loans": "Mortgage Loans", "mortgage_desc": "Explore our best homes and apartments, apply for a loan, and move in easily.", "view_all": "View All"}, "see_all": "See All", "view_all": "View All", "exchange_rates": "Exchange rates", "guest_mode": "Guest Mode", "guest_mode_desc": "You are in guest mode. Sign up or log in to your account to access your wallet and enjoy all features.", "sign_up": "Sign Up", "login": "<PERSON><PERSON>", "send_money_page": {"send_money_title": "Send Money", "select_bank": "Select Bank", "select_bank_desc": "Select a bank from the list where the recipient's account is located.", "search_bank_or": "Search Bank or Wallets", "banks": "Banks", "wallets": "Wallets", "continue": "Continue", "sender_bank_page": {"send_money_title": "Send Money", "recipent_description": "Enter the recipient account number on the selected  bank and send money.", "sender_name": "Sender Name", "enter_sender_name": "Enter Sender Name", "account_number": "Account Number", "enter_account": "Enter Account Number", "reason": "Reason", "enter_reason": "Enter Reason", "continue": "Continue"}, "recipent_wallet_page": {"recipent_phone": "Recipient Phone Number", "recipent_description": "Enter the recipient phone number on the selected  bank and send money.", "phone_number": "Phone Number", "enter_phone": "Enter Phone Number"}, "add_amount_page": {"send_money": "Send Money", "add_amount": "Add Amount", "description": "Enter the amount you wish to send to the recipient and submit.", "wallet_balance": "Wallet Balance", "amount_in_etb": "Amount in ETB", "allowed_minimum": "Allowed minimum amount is 5 USD", "continue": "Continue"}, "confirm_transfer_page": {"confirm_transfer": "Confirm Transfer", "description": "Review the amount and transaction details, then confirm the transfer.", "transaction_details": "Transaction Details", "sender_name": "Sender Name", "recipient_name": "Recipient Name", "recipient_account": "Recipient Account", "bank_name": "Bank Name", "exchange_rate": "Exchange Rate", "amount_in_usd": "Amount in USD", "amount_in_etb": "Amount in ETB", "total": "Total", "confirm": "Confirm", "amount": "Amount"}, "success_transfer_page": {"successfully_sent": "Successfully Sent", "successfully_desc": "Your transaction was completed successfully.", "transaction_detail": {"transaction_details": "Transaction Details", "sender_name": "Sender Name", "recipent_name": "Recipent Name", "recipent_account": "Recipent Account", "recipent_reference": "Recipent Reference", "payment_reference": "Payment Reference", "bank_name": "Bank Name", "exchange_rate": "Exchange Rate", "amount_in_usd": "Amount in USD", "amount_in_etb": "Amount in ETB", "total": "Total", "back_to_home": "Back to Home", "download_receipt": "Download Reciept"}}, "failed_repayment_page": {"failed_app": "Payment Failed!", "description": "Unfortunately, the transaction could not be completed. Please try again or check your payment details.", "back_to_home": "Back To Home"}}}, "home_page": {"home_tabs": {"home": "Home", "chat": "Cha<PERSON>", "my_loans": "My Loans", "transaction": "Transactions", "profile": "Profile"}, "home_page": {"hi": "Hi", "welcome_back": "Welcome Back!", "dollar_account": "Dollar Account", "birr_account": "<PERSON><PERSON><PERSON> Account", "wallet_balance": "Wallet Balance", "send_money": "Send Money", "wallet_transfer": "Wallet Transfer", "change_to_birr": "Change To Birr", "load_my_wallet": "Load My Wallet", "quick_wallet_transfer": "Quick Wallet Transfer", "package": {"package_and_loans": "Package & Loans", "gift_package": "Gift Package", "gift_desc": "Browse Gift Packages, purchase, and share the code for easy gifting.", "car_loans": "Car Loans", "car_desc": "Browse our top car selection, apply for a loan, and drive away with ease.", "mortgage_loans": "Mortgage Loans", "mortgage_desc": "Explore our best homes and apartments, apply for a loan, and move in easily.", "view_all": "View All"}, "recent_transactions": "Recent Transactions", "see_all": "See All", "exchange_rates": "Exchange rates"}}, "in_app_update": {}, "load_wallet": {"load_wallet_title": "Load Wallet", "description": "Enter the amount to wallet your account and get it ready for sending to anyone.", "continue": "Continue", "back_to_home": "Back to Home", "success_page": {"wallet_successfully": "Wallet Successfully Loaded!", "description": "Your wallet is now ready—feel free to send money to anyone with ease!", "transaction_detail": {"transaction_detail": "Transaction Details", "sender_name": "Sender Name", "recipent_email": "<PERSON><PERSON><PERSON>", "payment_reference": "Payment Reference", "payment_date": "Payment Date", "amount": "Amount", "total": "Total"}}, "failure_page": {"wallet_failed": "Wallet Loading Failed!", "descriptions": "Unable to load your wallet. Please check your connection or payment details and try again."}}, "mortgage_loan": {"mortgage_loan": "Mortgage Loans", "categories": "Categories", "all": "All", "apartment_list": "Apartment List", "description": "Choose your preferred apartment, review the details, and apply for your dream home effortlessly.", "bed_room": "Bed Room", "shower": "Shower", "mortgage_detail_page": {"apartment_type": "Apartment Type", "bed_room": "Bed Room", "bath_room": "Bath Room", "location": "Location", "area": "Area", "condition": "Condition", "description": "Description", "amenities": "Amenities", "similar_apartments": "Similar Apartments", "apply_for_loans": "Apply for Loans", "threed_view": "3D View", "mortgage_loan_application_page": {"mortgage_loans": "Mortgage Loans", "select_bank": "Select Bank", "selected": "Selected", "loan_packages": "Loan Packages(up front payment)", "loan_payment_info": "Loan Payment Information", "loan_payment_info_desc": "Loan information may vary based on your selected preferences", "upfront_payment_amount": "up front payment Amount", "loan_application_fee": "Loan Application Fee", "loan_period": "Loan Period", "payment_amount": "Payment Amount", "interest_rate": "Interest Rate", "daily_penalty_fee": "Daily Penalty Fee", "facilitation_fee": "Facilitation Fee", "down_payment": "Down Payment", "confirm_payment": "Confirm Payment", "total": "Total"}, "mortgage_loan_appliacation_doc_page": {"required_doc": "Required Documents", "required_doc_for": "Required Documents for Loan", "description": "Please prepare these documents; we will email you if further documents are needed.", "proof_of": {"proof_of_identity": "Proof of Identity ", "proof_of_identity_desc": "Please prepare a valid document, like your ID card, passport or driver's license.", "proof_of_residency": "Proof of Residence ", "proof_of_residency_desc": "Prepare your residential document to upload as part of the verification process.", "proof_of_income": "Income Proof ", "proof_of_income_desc": "Please prepare documents like your recent payslips, tax returns, or bank statements.", "proof_of_credity": " Credit Report ", "proof_of_credity_desc": "Please prepare your credit report to upload, as it helps for the loan application.", "continue": "Continue"}}, "mortgage_laon_apply_terms": {"loan_terms": "Loan Terms & Conditions", "our_terms": "Our Terms & Condition", "description": "Read our terms and conditions carefully before your continue with your application.", "i_agree": "I agree and Continue"}, "mortgage_loan_apply_page": {"loan_application": "Loan Application", "confirm_loan_appl": "Confirm Loan Application", "description": "Proceed to pay the application fee to finalize your loan request.", "wallet_balance": "Wallet Balance", "loan_application_fee": "Loan Application Fee", "transaction_details": "Transaction Details", "customer_name": "Customer Name", "loan_type": "Loan Type", "car_model": "Car Model", "loan_period": "Loan Period", "car_price": "Car Price", "down_payment": "Down Payment", "interest_rate": "Interest Rate", "application_fee": "Application Fee", "total": "Total"}, "success_car_loan_apply": {"success_appl": "Successfully Applied!", "description": "You have successfully applied for the loan and paid the application fee. We will contact you via email with the next steps to complete your loan process.", "transaction_details": "Transaction Details", "customer_name": "Customer Name", "loan_type": "Loan Type", "property_type": "Property Type", "car_model": "Canextr Model", "car_price": "Car Price", "loan_period": "Loan Period", "down_payment": "Down Payment", "payment_reference": "Payment Reference", "application_fee": "Application Fee", "application_date": "Application Date", "total": "Total", "back_to_loans": "Back To Loans", "info": "Info", "application_code": "Application Code", "application_desc": "This unique code is essential for tracking and processing your loan application. Please keep it secure and share it only with authorized personnel if required. You can use this code to check the status of your application or for any inquiries. Thank you for choosing us to assist with your financial needs!", "loan_application_code": "Loan Application Code", "copied": "<PERSON>pied"}}}, "my_loan": {"my_loans": "My Loans", "my_loan_tabs": {"active": "Active", "pending": "Pending", "completed": "Completed", "preparing": "Preparing"}, "approved_on": "Approved on:", "loan_details_page": {"loan_details": "<PERSON>an <PERSON>", "model": "Model", "loan_info": "Loan <PERSON>", "car_detail": "Car Detail", "payment_history": "Payment History", "mortgage_payment_history": "Mortgage Payment History", "loan_payment_info": "Loan Payment Information", "loan_description": "Details information about car loan Application", "bank_name": "Bank Name", "down_payment": "Down Payment", "upfront_payment_amount": "up front payment Amount", "payment_term": {"payment_term": "Payment Term", "payment_term_desc": "Choose your preferred payment term for your loan repayment.", "pay_by_connect": "Pay By Connect Wallet", "pay_by_agent": "Pay Via Agent"}, "loan_period": "Loan Period", "payment_amount": "Payment Amount", "interest_rate": "Interest Rate", "daily_penalty_fee": "Daily Penalty Fee", "facilitation_fee": "Facilitation Fee", "month": "Month", "year": "Year", "years": "Years", "car_detail_info": {"car_description": "Details information about this car", "body_type": "Body Type", "transmission": "Transmission", "max_speed": "Max Speed", "make": "Make", "model": "Model", "color": "Color", "no_of_seats": "No of Seats", "drive_train": "drive_train", "engine_size": "Engine Size", "fuel_type": "Fuel Type", "mile_age": "MileAge", "fuel_efficiency": "Fuel Efficiency", "horse_power": "HorsePower", "manufactureYear": "Manufacture Year"}, "apartment_detail_info": {"apartment_info": "Apartment Information", "description": "Description", "apartment_type": "Apartment Type", "bed_room": "Bed Room", "bath_room": "Bath Room", "location": "Location", "area": "Area", "condition": "Condition", "amenities": "Amenities"}, "approved_loan_info": {"congratulation": "Congratulations, your loan application has been approved! To finalize, please proceed with your Down Payment payment.", "down_payment_info": "Down Payment Information", "out_of": "Out of", "down_payment": "Down Payment", "monthly_repayment": "Monthly Repayment", "interest_rate": "Interest Rate", "pay_down_payment": "Pay Down Payment"}, "active_loan_info": {"this_month": "This Month Bill", "this_month_desc": "This month's repayment may vary based on your plan.", "this_month_repay": "This Month Repayment", "payment_period": "Payment Period", "to_be_paid_by": "To be paid by", "payment_started": "Payment Started at", "your_repayment_code": "Your Repayment Code", "active_pay_desc_one": "If your loan is not paid within ", "active_pay_desc_two": "penalties will apply. ", "day": "day", "days": "days", "payment_due": "Payment Due", "penalty_fee": "Penalty Fee", "total": "Total", "payment_inormation": "Payment Information", "penalty_desc": "Failure to pay your monthly installment will result in your loan being flagged as Non-Performing (NPL).", "down_payment": "Down Payment", "paid_amount": "<PERSON><PERSON>", "paid": "Paid", "out_of": "Out of", "monthly_repayment": "Monthly Repayment", "repayment_graph": "Repayment Graph", "more_report": "More Report", "early_repay": "Early Repayment", "on_time_repay": "On Time Repayment", "late_repay": "Late Reayment", "remaining_amount": "Remaining Amount", "expected_end_date": "Expected End Date", "interest_rate": "Interest Rate", "based_on": "Based On Agreement", "repay": "<PERSON>ay"}, "completed_loan_info": {"paid_amount": "<PERSON><PERSON>", "out_of": "Out of", "repaid_with_in": "Repaid With in", "completed_at": "Completed at"}}, "confirm_repayment_page": {"laon_repayment": "Loan Repayment", "confirm_down_payment": "Confirm Down Payment", "wallet_balance": "Wallet Balance", "down_payment_amount": "Down Payment Amount", "loan_application_fee": "Loan Application Fee", "transaction_details": "Transaction Details", "customer_name": "Customer Name", "loan_type": "Loan Type", "car_model": "Car Model", "loan_period": "Loan Period", "down_payment": "Down Payment", "monthly_repayment": "Monthly Payment", "interest_rate": "Interest Rate", "facilitation_fee": "Facilitation Fee", "application_date": "Application Date", "total": "Total", "confirm_payment": "Confirm Payment"}, "success_repayment_page": {"success_app": "Successfully Applied!", "upfront_description": "Your Down Payment payment has been successfully paid. We'll reach out via email with the next steps to complete your loan process.", "repayment_desc": "Your payment was successful! Thank you for making your monthly Loan payment", "transaction_details": "Transaction Details", "customer_name": "Customer Name", "loan_type": "Loan Type", "loan_application_reference": "Loan Application Reference", "payment_reference": "Payment Reference", "facilitation_fee": "Facilitation Fee", "paidVia": "<PERSON><PERSON>", "application_date": "Application Date", "total": "Total", "back_to_loans": "Back To My Loans"}, "failed_repayment_page": {"failed_app": "Payment Failed!", "description": "Unfortunately, the transaction could not be completed. Please try again or check your payment details.", "back_to_home": "Back To Home"}, "payment_detail_page": {"payment_details": "Payment Details", "customer_name": "Customer Name", "mortgage_name": "Mortgage Name", "loan_type": "Loan Type", "loan_application_reference": "Loan Application Reference", "payment_reference": "Payment Reference", "payment_date": "Payment Date", "facilitation_fee": "Facilitation Fee", "payment_amount": "Payment Amount", "late_payment_fee": "Late Payment Fee", "interest_rate": "Interest Rate", "total": "Total", "get_reciept": "Get Reciept"}, "pay_with_agent_page": {"agent_code": "Agent Code", "your_loan_repy_code": "Your Loan Repayment Code", "description": "Proceed with your Monthly Payment or add an extra amount to pay towards your mortgage.", "desc_notice": "Please share your unique code with the agent to complete your loan payment. You can visit any nearby authorized agent or payment center.", "repayment_detail": "Repayment Details", "customer_name": "Customer Name", "loan_type": "Loan Type", "loan_period": "Loan Period", "monthly_payment": "Monthly Payment", "facilitation_fee": "Facilitation Fee", "interest_rate": "Interest Rate", "loan_application_reference": "Loan Application Reference", "payment_reference": "Payment Reference", "application_date": "Application Date", "amount": "Amount", "reyment_amount": "Repayment Amount", "back_to_loans": "Back To My Loans"}}, "notifications": {"notifications": "Notifications", "today": "Today", "yesterday": "Yesterday", "all_notification": "All Notifications", "notification_details": "Notification Details", "no_notification": "No Notifications Yet", "no_notification_desc": "You currently have no notifications. You will receive alerts for transaction you make within the app."}, "onboarding": {"title": "Your Trusted Transfer Solution", "description": "Send money to loved ones effortlessly and securely. Experience fast, reliable transfers with just a few taps and make every transaction count.", "signup": "Sign Up", "login": "<PERSON><PERSON>", "continue_as_guest": "Continue as Guest"}, "orders": {"orders": "Orders", "order_tabs": {"all": "All", "sent": "<PERSON><PERSON>", "recieved": "Recieved"}, "today": "Today", "yesterday": "Yesterday", "all_notification": "All Notifications", "order_detail_sent_page": {"order_details": "Order Details", "package_info": "Package Information", "purchased_at": "Purchased at:", "payment_details": "Payment Details", "order_code": "Order Code", "order_number": "Order Number", "customer_name": "Customer Name", "recipent_name": "Recipent Name", "recipent_phone": "Recipent Phone", "package_name": "Package Name", "package_reference": "Package Reference", "payment_date": "Payment Date", "amount": "Amount", "total": "Total", "delivery_status": "Delivery Status", "get_reciept": "Get Reciept"}, "order_detail_recieved_page": {"order_details": "Order Details", "gift_info": "Gift information", "sent_at": "Sent at:", "pickup_address": "Pickup address", "nearby": "Nearby", "pickup_desc": "You can pick your gift from any ", "sender_detail": "Sender Detail", "order_code": "Order Code", "customer_name": "Customer Name", "recipent_name": "Recipent Name", "recipent_phone": "Recipent Phone", "package_name": "Package Name", "delivery_status": "Delivery Status", "send_message": "Send Message", "message": "Message"}}, "profile": {"profile": "Profile", "account_info": {"account_information": "Account Informations", "profile_info": "Profile Information", "change_pin": "Change PIN", "add_phone": "Add Phone", "verify_phone": "Verify Phone", "add_email": "Add <PERSON>", "verify_email": "<PERSON><PERSON><PERSON>", "phone_number": "Phone Number", "subscriptions": "Subscriptions", "language": "Language"}, "faq_and": {"faq_and_legacy": "Faq & Legacy", "faqs": "Faqs", "privacy_policy": "Privacy Policy", "terms_and_conditions": "Terms & Conditions", "customer_support": "Customer Support", "unlink_device": "Unlink Device", "logout": "Logout"}, "upload_photo": {"upload_profile": "Upload Profile Picture", "description: ": "Take a picture or choose from the gallery.", "upload_from_gallery": "Upload form Gallery", "take_phote": "Take Photo", "browse": "Upload", "camera": "Camera", "delete": "Delete"}, "change_pin": {"change_pin": "Change PIN", "description: ": "Enter your current PIN and set a new one to enhance your security..", "old_pin": "old PIN", "new_pin": "New PIN", "confirm_pin": "Confirm new PIN"}, "profile_info": {"profile_information": "Profile Information", "description": "Check and Update your profile informations", "first_name": "First Name", "last_name": "Last Name", "middle_name": "Middle Name", "gender": "Gender", "male": "Male", "female": "Female", "phone_number": "Phone Number", "email_address": "Email Address", "country": "Country", "city": "City", "date_of_birth": "Date of Birth", "update_profile": "Update Profile"}, "add_phone": {"add_phone_number": "Add Phone Number", "description": "Enter your phone number and verify it to start receiving money directly to your phone.", "phone_number": "Phone Number", "next": "Next", "phone_verification": {"title": "Phone Verification", "description": "We've sent a verification code to your phone number. Please enter the code to confirm your phone number.", "sec": "Sec", "didnt_receive": "Didn’t receive code?", "recend_otp": "Resend OTP", "verify": "Verify"}}, "add_email": {"add_email": "Add <PERSON>", "description": "Enter your email and verify it to start receiving money directly to your email.", "email_address": "Email Address", "next": "Next", "email_verification": {"title": "Email Verification", "description": "We've sent a verification code to your email. Please enter the code to confirm your email address.", "sec": "Sec", "didnt_receive": "Didn’t receive code?", "recend_otp": "Resend OTP", "verify": "Verify"}}, "change_language": {"change_language": "Change Language", "description": "Select your preferred language for easy communication.", "english": "English", "amharic": "አማርኛ", "tigrign": "ትግርኛ", "afan_oromo": "<PERSON><PERSON><PERSON>", "af_somali": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cancel": "Cancel", "done": "Done"}, "customer_support": {"customer_support": "Customer Support", "short_code": "Short Code", "mobile_number": "Mobile Number", "email": "Email"}, "unlink_device": {"unlink_device": "Unlink Device", "description": "Are you sure you want to unlink this device?", "cancel": "Cancel", "unlink": "Unlink"}, "logout": {"logout": "Logout", "description": "Are you sure you want to logout?", "cancel": "Cancel"}}, "quick_wallet_transfer": {"quick_wallet_transfer": "Quick Wallet Transfer", "description": "Select the wallet account you want to use to send money to this beneficiary.", "continue": "Continue", "dollar_account": "Dollar Account", "birr_account": "<PERSON><PERSON><PERSON> Account", "add_amount_page": {"add_amount": "Add Amount", "description": "Enter the amount you wish to send to the recipient and submit.", "wallet_balance": "Wallet Balance"}, "confirm_transfer_page": {"confirm_transfer": "Confirm Transfer", "description": "Review the amount and transaction details, then confirm the transfer.", "transaction_details": "Transaction Details", "sender_name": "Sender Name", "recipient_email": "Recipient Email", "recipient_phone_number": "Recipient Phone Number", "date": "Date", "amount_in_usd": "Amount in USD", "total": "Total", "confirm": "Confirm"}, "success_transfer_page": {"successfully_sent": "Successfully Sent", "successfully_desc": "Your transaction was completed successfully.", "transaction_detail": {"transaction_details": "Transaction Details", "sender_name": "Sender Name", "recipent_name": "Recipent Name", "recipent_Email": "<PERSON><PERSON><PERSON>", "recipent_reference": "Recipent Reference", "payment_reference": "Payment Reference", "payment_date": "Payment Date", "amount_in_usd": "Amount in USD", "total": "Total", "back_to_home": "Back to Home"}}}, "send_money": {"send_money_title": "Send Money", "select_bank": "Select Bank", "select_bank_desc": "Select a bank from the list where the recipient's account is located.", "search_bank_or": "Search Bank or Wallets", "banks": "Banks", "wallets": "Wallets", "continue": "Continue", "recipent_bank_page": {"recipent_account": "Recipient Account Number", "recipent_description": "Enter the recipient account number on the selected  bank and send money.", "account_number": "Account Number", "enter_account": "Enter Account Number"}, "recipent_wallet_page": {"recipent_phone": "Recipient Phone Number", "recipent_description": "Enter the recipient phone number on the selected  bank and send money.", "phone_number": "Phone Number", "enter_phone": "Enter Phone Number"}, "add_amount_page": {"add_amount": "Add Amount", "description": "Enter the amount you wish to send to the recipient and submit.", "wallet_balance": "Wallet Balance", "amount_in_etb": "Amount in ETB"}, "confirm_transfer_page": {"confirm_transfer": "Confirm Transfer", "description": "Review the amount and transaction details, then confirm the transfer.", "transaction_details": "Transaction Details", "sender_name": "Sender Name", "recipient_name": "Recipient Name", "recipient_account": "Recipient Account", "date": "Date", "amount_in_usd": "Amount in USD", "amount_in_etb": "Amount in ETB", "total": "Total", "confirm": "Confirm", "amount": "Amount"}, "success_transfer_page": {"successfully_sent": "Successfully Sent", "successfully_desc": "Your transaction was completed successfully.", "transaction_detail": {"transaction_details": "Transaction Details", "sender_name": "Sender Name", "recipent_name": "Recipent Name", "recipent_account": "Recipent Account", "recipent_reference": "Recipent Reference", "payment_reference": "Payment Reference", "payment_date": "Payment Date", "amount_in_usd": "Amount in USD", "amount_in_etb": "Amount in ETB", "total": "Total", "back_to_home": "Back to Home"}}}, "transactions": {"transactions": "Transactions", "transaction_tabs": {"all": "All", "my_orders": "My Orders", "send_money": "Send Money", "transfer": "Transfer"}, "today": "Today", "yesterday": "Yesterday", "all_notification": "All Notifications", "no_transaction_yet": "No Transactions Yet", "no_transaction_yet_desc": "You haven't made any transactions so far. Once you complete a payment, it will show up here.", "select_date": {"select_date_range": "Select Date Range", "cancel": "Cancel", "done": "Done"}, "transaction_detail_page": {"transaction_detail": "Transaction Details", "sender_name": "Sender Name", "recipent_name": "Recipent Name", "recipent_account": "Recipent Account", "date": "Date", "recipent_email": "<PERSON><PERSON><PERSON>", "payment_number": "Payment Number", "transfer_type": "Transfer Type", "reference_number": "Reference Number", "amount_in_usd": "Amount in USD", "amount_in_etb": "Amount in ETB", "vat": "VAT", "service_fee": "Service Fee", "transaction_status": "Transaction Status", "amount": "Amount", "total_amount": "Total Amount", "get_reciept": "Get Reciept"}}, "transfer_to_wallet": {"transfer_to_wallet": "Transfer to Wallet", "who_are_you_sending": "Who are you sending?", "description": "Search for the recipient using their Connect email address and send money directly to their wallet.", "email_address": "Email Address", "email": "Email", "phone_number": "Phone Number", "enter_phone_number": "Enter Phone Number", "enter_email_address": "Enter Email Address", "continue": "Continue", "contact_page": {"contact_list": "Contacts List", "description": "Select a contact with a CONNECT Wallet and transfer funds directly to their wallet."}, "add_amount_page": {"add_amount": "Add Amount", "description": "Enter the amount you wish to send to the recipient and submit.", "wallet_balance": "Wallet Balance"}, "confirm_transfer_page": {"confirm_transfer": "Confirm Transfer", "description": "Review the amount and transaction details, then confirm the transfer.", "transaction_details": "Transaction Details", "sender_name": "Sender Name", "recipient_name": "Recipient Name", "recipient_phone": "Recipient Phone", "date": "Date", "amount_in_usd": "Amount in USD", "total": "Total", "confirm": "Confirm"}, "success_transfer_page": {"successfully_sent": "Successfully Sent", "successfully_desc": "Your transaction was completed successfully.", "transaction_detail": {"transaction_details": "Transaction Details", "sender_name": "Sender Name", "recipent_name": "Recipent Name", "recipent_account": "Recipent Account", "recipent_reference": "Recipent Reference", "payment_reference": "Payment Reference", "payment_date": "Payment Date", "amount_in_usd": "Amount in USD", "total": "Total", "back_to_home": "Back to Home"}}}, "user": {}}