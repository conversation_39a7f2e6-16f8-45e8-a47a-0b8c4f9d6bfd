import 'package:cbrs/features/add_money/domain/entities/add_money_response.dart';
import 'package:flutter/material.dart';

class AddMoneyResponseModel {
  final int statusCode;
  final bool success;
  final AddMoneyData? data;
  final String message;

  AddMoneyResponseModel({
    required this.statusCode,
    required this.success,
    this.data,
    required this.message,
  });

  factory AddMoneyResponseModel.fromJson(dynamic json) {
    if (json is String) {
      return AddMoneyResponseModel(
        statusCode: 400,
        success: false,
        message: json,
        data: null,
      );
    }

    if (json is! Map<String, dynamic>) {
      return AddMoneyResponseModel(
        statusCode: 400,
        success: false,
        message: 'Invalid response format',
        data: null,
      );
    }

    final Map<String, dynamic> jsonMap = json;
    final bool isSuccess = jsonMap['success'] == true;
    final dynamic messageData = jsonMap['message'];

    // Get transaction data from either 'transaction' or 'data' key
    final transactionData = jsonMap['transaction'] ?? jsonMap['data'];

    if (transactionData == null) {
      return AddMoneyResponseModel(
        statusCode: jsonMap['statusCode'] as int? ?? 400,
        success: isSuccess,
        message: messageData?.toString() ?? '',
        data: null,
      );
    }

    

    return AddMoneyResponseModel(
      statusCode: jsonMap['statusCode'] as int? ?? 200,
      success: isSuccess,
      data: AddMoneyData.fromJson(transactionData as Map<String, dynamic>),
      message: messageData?.toString() ?? '',
    );
  }
}

class AddMoneyData {
  final String id;
  final String senderId;
  final String? sessionId;
  final String senderName;
  final String? senderPhone;
  final String? cardNumber;
  final String? senderEmail;
  final String transactionOwner;
  final String beneficiaryId;
  final String beneficiaryName;
  final String? beneficiaryPhone;
  final String beneficiaryAccountNo;
  final String? beneficiaryEmail;
  final String? bankName;
  final String? bankCode;
  final String transactionType;
  final double billAmount;
  final String originalCurrency;
  final double serviceCharge;
  final double vat;
  final double totalAmount;
  final String billRefNo;
  final String status;
  final String authorizationType;
  final String? walletFTNumber;

  AddMoneyData({
    required this.id,
    required this.senderId,
    this.sessionId,
    required this.senderName,
    this.senderPhone,
    this.cardNumber,
    this.senderEmail,
    required this.transactionOwner,
    required this.beneficiaryId,
    required this.beneficiaryName,
    this.beneficiaryPhone,
    required this.beneficiaryAccountNo,
    this.beneficiaryEmail,
    this.bankName,
    this.bankCode,
    required this.transactionType,
    required this.billAmount,
    required this.originalCurrency,
    required this.serviceCharge,
    required this.vat,
    required this.totalAmount,
    required this.billRefNo,
    required this.status,
    required this.authorizationType,
    this.walletFTNumber,
  });

  factory AddMoneyData.fromJson(Map<String, dynamic> json) {
    debugPrint("😔total amount of json ${json['totalAmount']}");
    return AddMoneyData(
      id: json['id'] as String,
      senderId: json['senderId'] as String,
      sessionId: json['sessionID'] as String?,
      senderName: json['senderName'] as String,
      senderPhone: json['senderPhone'] as String?,
      cardNumber: json['cardNumber'] as String?,
      senderEmail: json['senderEmail'] as String?,
      transactionOwner: json['transactionOwner'] as String,
      beneficiaryId: json['beneficiaryId'] as String,
      beneficiaryName: json['beneficiaryName'] as String,
      beneficiaryPhone: json['beneficiaryPhone'] as String?,
      beneficiaryAccountNo: json['beneficiaryAccountNo'] as String,
      beneficiaryEmail: json['beneficiaryEmail'] as String?,
      bankName: json['bankName'] as String?,
      bankCode: json['bankCode'] as String?,
      transactionType: json['transactionType'] as String,
      billAmount: (json['billAmount'] as num).toDouble(),
      originalCurrency: json['originalCurrency'] as String,
      serviceCharge: (json['serviceCharge'] as num).toDouble(),
      vat: (json['VAT'] as num).toDouble(),
      totalAmount: (json['totalAmount'] as num).toDouble(),
      billRefNo: json['billRefNo'] as String,
      status: json['status'] as String,
      authorizationType: json['authorization_type'] as String,
      walletFTNumber: json['walletFTNumber'] as String?,
    );
  }
}
