import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:rxdart/rxdart.dart';

class ConnectivityService {
  final Connectivity _connectivity = Connectivity();

  Future<bool> hasInternetConnection({
    Duration timeout = const Duration(seconds: 5),
  }) async {
    if (kIsWeb) return true;

    try {
      final hosts = [
        '*******',
        '*******',
        'google.com',
        'cloudflare.com',
      ];

      for (final host in hosts) {
        try {
          final result = await InternetAddress.lookup(host).timeout(timeout);
          if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
            return true;
          }
        } catch (_) {
          continue;
        }
      }
      return false;
    } catch (_) {
      return false;
    }
  }

  Stream<ConnectivityResult> get connectivityStream {
    if (kIsWeb) return Stream.value(ConnectivityResult.wifi);

    return _connectivity.onConnectivityChanged
        .asyncMap((result) async {
          if (result != ConnectivityResult.none) {
            final hasInternet = await hasInternetConnection().timeout(
              const Duration(milliseconds: 1500),
              onTimeout: () => false,
            );
            if (!hasInternet) {
              return ConnectivityResult.none;
            }
          }
          return result;
        })
        .distinct()
        .debounceTime(const Duration(milliseconds: 300))
        .handleError(
          (_) {
            return ConnectivityResult.none;
          },
        );
  }

  Future<ConnectivityResult> checkConnectivity({
    Duration timeout = const Duration(seconds: 5),
  }) async {
    if (kIsWeb) return ConnectivityResult.wifi;

    try {
      final result = await _connectivity.checkConnectivity();
      if (result != ConnectivityResult.none) {
        final hasInternet = await hasInternetConnection(timeout: timeout);
        if (!hasInternet) {
          // Double check before declaring no connection
          await Future.delayed(const Duration(seconds: 1));
          final secondCheck = await hasInternetConnection(
            timeout: const Duration(seconds: 2),
          );
          if (!secondCheck) {
            return ConnectivityResult.none;
          }
        }
      }
      return result;
    } catch (e) {
      debugPrint('Connectivity check error: $e');
      return ConnectivityResult.none;
    }
  }
}
