import 'package:cbrs/core/common/global_variable.dart';
import 'package:cbrs/core/constants/storage_keys.dart';
import 'package:cbrs/core/services/connectivity/connectivity_controller.dart';
import 'package:cbrs/core/services/device/device_controller.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/services/storage/hive_box_manager.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class NavigationService {
  NavigationService({
    required HiveBoxManager hiveBoxManager,
    required AuthLocalDataSource authLocalDataSource,
  })  : _hiveBoxManager = hiveBoxManager,
        _authLocalDataSource = authLocalDataSource;
  final HiveBoxManager _hiveBoxManager;
  final AuthLocalDataSource _authLocalDataSource;
  bool _isCheckingConnectivity = false;
  bool _isInitialLaunch = true;

  Future<String?> handleRedirect(
    String location,
  ) async {
    try {
      debugPrint('Loccccation $location');
      if (location == '/splashLoadingScreen') {
        return '/splashLoadingScreen';
      }
      debugPrint('check  1');

      if (location == AppRouteName.connectionLost) {
        return null;
      }

      debugPrint('check 2');

      if (location == AppRouteName.videoCall) {
        return AppRouteName.videoCall;
      }
      debugPrint('check  3');

      if (!_isCheckingConnectivity) {
        _isCheckingConnectivity = true;
        final connectivityController = Get.find<ConnectivityController>();
        if (!connectivityController.isConnected) {
          _isCheckingConnectivity = false;
          return AppRouteName.connectionLost;
        }
        _isCheckingConnectivity = false;
      }
      debugPrint('check  4');

      if (_isPublicRoute(location) && location != AppRouteName.onboarding) {
        return null;
      }

      debugPrint(
          'check 5 GlobalVariable.isDeviceVerified ${GlobalVariable.isDeviceVerified}');

      if (GlobalVariable.isDeviceVerified || _isProtectedRoute(location)) {
        debugPrint(
            'check 6  _isProtectedRoute ${_isProtectedRoute} location $location');

        return _handleAuthenticatedUser(location);
      } else {
        debugPrint('check  7');

        return _handleUnauthenticatedUser(location);
      }
    } on DioException catch (e) {
      debugPrint('check  8');

      if (e.type == DioExceptionType.connectionError ||
          e.message?.toLowerCase().contains('connection reset') == true) {
        await _authLocalDataSource.clearAuthToken();
        return AppRouteName.connectionLost;
      }
      debugPrint('check  9');

      if (_isTimeoutError(e.type)) {
        await _authLocalDataSource.clearAuthToken();
        return AppRouteName.connectionLost;
      }
      debugPrint('check  10');

      if (e.response?.statusCode == 401 || e.response?.statusCode == 403) {
        await _authLocalDataSource.clearAuthToken();
        return AppRouteName.tokenDeviceLogin;
      }

      debugPrint('check  11');

      if (e.type == DioExceptionType.unknown) {
        return AppRouteName.connectionLost;
      }
      debugPrint('check 13 ');

      return AppRouteName.connectionLost;
    } catch (e) {
      return AppRouteName.connectionLost;
    }
  }

  bool _isTimeoutError(DioExceptionType type) {
    return type == DioExceptionType.connectionTimeout ||
        type == DioExceptionType.sendTimeout ||
        type == DioExceptionType.receiveTimeout;
  }

  Future<String?> _handleAuthenticatedUser(String location) async {
    try {
      if (location == AppRouteName.tokenDeviceLogin) {
        await _authLocalDataSource.clearAuthToken();
        await _authLocalDataSource.clearUserData();
        return AppRouteName.tokenDeviceLogin;
      }

      if (_isProtectedRoute(location)) {
        debugPrint("check route  _isProtectedRoute");
        return null;
      }

      if (_isPublicRoute(location)) {
        return AppRouteName.tokenDeviceLogin;
      }

      return AppRouteName.tokenDeviceLogin;
    } catch (e) {
      return AppRouteName.tokenDeviceLogin;
    }
  }

  String? _handleUnauthenticatedUser(String location) {
    final isFirstTimer = _hiveBoxManager.mainBox
        .get(StorageKeys.firstTimer, defaultValue: true) as bool;

    debugPrint(
      'global is device  ${GlobalVariable.isDeviceRegisteredOnConnect}',
    );

    debugPrint(
      'global is device  ${GlobalVariable.deviceCheckNetworkError}',
    );
    if (GlobalVariable.isDeviceRegisteredOnConnect) {
      return AppRouteName.tokenDeviceLogin;
    }
    if (GlobalVariable.deviceCheckNetworkError) {
      return AppRouteName.connectionLost;
    }
    if (isFirstTimer) {
      return AppRouteName.onboarding;
    }

    return _isPublicRoute(location) ? null : AppRouteName.guestHomePage;
  }

  bool _isPublicRoute(String location) {
    final publicRoutes = [
      AppRouteName.onboarding,
      AppRouteName.splashOnboardig,

      AppRouteName.signIn,
      AppRouteName.signUp,
      AppRouteName.forgotPin,
      AppRouteName.verifyEmail,
      AppRouteName.verifyOtp,
      AppRouteName.createPin,
      AppRouteName.guestHomePage,
      AppRouteName.connectionLost,
    ];

    if (location == AppRouteName.onboarding) {
      final isFirstTimer = _hiveBoxManager.mainBox
          .get(StorageKeys.firstTimer, defaultValue: true) as bool;
      return isFirstTimer;
    }

    return publicRoutes.contains(location);
  }

  bool _isProtectedRoute(String location) {
    final protectedPaths = [
      '/main',
      'notifications',
      AppRouteName.tokenDeviceLogin,
      'send-usd',
      'send-etb',
      'transfer-to-wallet-usd',
      'transfer-to-wallet-birr',
      'change-to-birr',
      'load-to-wallet',
      'transactions',
      'profile-info',
      'customer-support',
      'faq',
      'privacy-policy',
      'terms-and-conditions',
      'quick-wallet-transfer',
      'gift-packages',
      'merchant-payment',
      'identity-verification',
    ];

    return protectedPaths.any((path) => location.contains(path));
  }

  void resetInitialLaunch() {
    _isInitialLaunch = true;
  }
}
