
import 'package:cbrs/features/mortgage_loan/domain/entities/loan_payment_info.dart';
import 'package:cbrs/features/mortgage_loan/domain/entities/mortgage_bank.dart';
import 'package:equatable/equatable.dart';

abstract class MortgageBankState extends Equatable {
  const MortgageBankState({
    this.banks = const [],
    this.selectedBank,
    this.selectedUpfrontPayment,
    this.paymentInfo,
    this.productId,
    this.termsContent,
    this.error,
  });
  final List<MortgageBank> banks;
  final MortgageBank? selectedBank;
  final String? selectedUpfrontPayment;
  final LoanPaymentInfo? paymentInfo;
  final String? productId;
  final String? termsContent;
  final String? error;

  @override
  List<Object?> get props => [
        banks,
        selectedBank,
        selectedUpfrontPayment,
        paymentInfo,
        productId,
        termsContent,
        error,
      ];
}

class MortgageBankInitial extends MortgageBankState {
  const MortgageBankInitial() : super();
}

class MortgageBankLoading extends MortgageBankState {}

class MortgageBankLoaded extends MortgageBankState {
  const MortgageBankLoaded({
    super.banks = const [],
    super.selectedBank,
    super.selectedUpfrontPayment,
    super.paymentInfo,
    super.productId,
    super.termsContent,
    this.error,
  });
  @override
  final String? error;

  MortgageBankLoaded copyWith({
    List<MortgageBank>? banks,
    MortgageBank? selectedBank,
    String? selectedUpfrontPayment,
    LoanPaymentInfo? paymentInfo,
    String? productId,
    String? termsContent,
    String? error,
  }) {
    return MortgageBankLoaded(
      banks: banks ?? this.banks,
      selectedBank: selectedBank ?? this.selectedBank,
      selectedUpfrontPayment:
          selectedUpfrontPayment ?? this.selectedUpfrontPayment,
      paymentInfo: paymentInfo ?? this.paymentInfo,
      productId: productId ?? this.productId,
      termsContent: termsContent ?? this.termsContent,
      error: error,
    );
  }
}

class MortgageBankError extends MortgageBankState {
  const MortgageBankError(this.message);
  final String message;
}