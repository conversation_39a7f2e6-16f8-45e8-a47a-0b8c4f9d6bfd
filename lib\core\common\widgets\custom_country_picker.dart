import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:country_flags/country_flags.dart';
import 'package:cbrs/core/res/country_res.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomCountryPicker extends StatefulWidget {
  final Function(String countryName) onCountrySelected;
  final ThemeData theme;
  String initialCountry;

  CustomCountryPicker({
    super.key,
    required this.onCountrySelected,
    required this.theme,
    required this.initialCountry,
  });

  @override
  State<CustomCountryPicker> createState() => CustomCountryPickerState();
}

class CustomCountryPickerState extends State<CustomCountryPicker> {
  final TextEditingController _searchController = TextEditingController();
  List<Map<String, String>> _filteredCountries = [];
  bool _isBottomSheetOpen = false;

  @override
  void initState() {
    super.initState();
    _filteredCountries = CountryRes.countries;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void showCountryPicker() {
    setState(() => _isBottomSheetOpen = true);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => Container(
          margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          height: MediaQuery.of(context).size.height * 0.9,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(
                top: Radius.circular(24.r), bottom: Radius.circular(24.r)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: Column(
            children: [
              // Bottom sheet handle

              // Title and close button
              Container(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Country List ${widget.initialCountry}',
                      style: GoogleFonts.plusJakartaSans(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: widget.theme.colorScheme.onSurface,
                      ),
                    ),
                    GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: Icon(
                        Icons.close,
                        color: widget.theme.colorScheme.onSurface,
                        size: 24.h,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 20.h,
              ),
              // Search bar
              Container(
                decoration: BoxDecoration(
                  color: widget.theme.colorScheme.onTertiary,
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: TextField(
                  controller: _searchController,
                  onChanged: (query) {
                    setState(() {
                      if (query.isEmpty) {
                        _filteredCountries = CountryRes.countries;
                      } else {
                        _filteredCountries = CountryRes.countries
                            .where((country) => country['name']!
                                .toLowerCase()
                                .contains(query.toLowerCase()))
                            .toList();
                      }
                    });
                  },
                  textInputAction: TextInputAction.search,
                  autocorrect: false,
                  enableSuggestions: false,
                  decoration: InputDecoration(
                    hintText: 'Search',
                    hintStyle: GoogleFonts.plusJakartaSans(
                      fontSize: 14.sp,
                      color: Color(0xFF000000).withOpacity(0.4),
                    ),
                    prefixIcon: Icon(
                      Icons.search,
                      size: 20.h,
                      color: Color(0xFF000000).withOpacity(0.4),
                    ),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? GestureDetector(
                            onTap: () {
                              setState(() {
                                _searchController.clear();
                              });
                            },
                            child: Icon(
                              Icons.close_rounded,
                              size: 20.h,
                              color: Color(0xFF000000).withOpacity(0.4),
                            ),
                          )
                        : null,
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 16.w,
                      vertical: 12.h,
                    ),
                  ),
                ),
              ),
              // Country list

              SizedBox(
                height: 16.h,
              ),
              Expanded(
                child: ListView.builder(
                  itemCount: _filteredCountries.length,
                  itemBuilder: (context, index) {
                    final country = _filteredCountries[index];
                    return Column(
                      children: [
                        InkWell(
                          onTap: () {

                            setState(() {
                              widget.initialCountry = country['name']!;
                            });
                            widget.onCountrySelected(country['name']!);
                            Navigator.pop(context);
                            _searchController.clear();
                          },
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                              vertical: 8.h,
                            ),
                            child: Row(
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(4),
                                  child: CountryFlag.fromCountryCode(
                                    country['code']!,
                                    height: 28.h,
                                    width: 40.w,
                                  ),
                                ),
                                SizedBox(width: 8.w),
                                Expanded(
                                  child: Text(
                                    country['name']!,
                                    style: GoogleFonts.plusJakartaSans(
                                      fontSize: 14.sp,
                                      color: Colors.grey[800],
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        if (index < _filteredCountries.length - 1)
                          SizedBox(
                            height: 8.h,
                          )
                        // Divider(
                        //   height: 1,
                        //   color: Colors.grey.withOpacity(0.2),
                        // ),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    ).whenComplete(() {
      setState(() {
        _isBottomSheetOpen = false;
        _searchController.clear();
        _filteredCountries = CountryRes.countries;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: showCountryPicker,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 20,
          vertical: 15,
        ),
        decoration: BoxDecoration(
          color: widget.theme.colorScheme.onTertiary,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Text(
              widget.initialCountry.isEmpty
                  ? 'Select Country'
                  : widget.initialCountry,
              style: GoogleFonts.outfit(
                color: widget.initialCountry.isEmpty
                    ? Colors.grey
                    : widget.theme.colorScheme.onSurface,
                fontSize: widget.initialCountry.isEmpty ? 14.sp : 16.sp,
              ),
            ),
            const Spacer(),
            Icon(
              Icons.keyboard_arrow_down_rounded,
              color: Color(0xFFAAAAAA),
              size: 20.h,
            ),
          ],
        ),
      ),
    );
  }
}
