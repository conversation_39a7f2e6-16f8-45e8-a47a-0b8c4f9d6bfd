import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:cbrs/features/identity_verification/domain/usecases/upload_document_usecase.dart';
import 'package:cbrs/features/identity_verification/domain/usecases/params/identity_verification_params.dart';
import 'identity_verification_event.dart';
import 'identity_verification_state.dart';

class IdentityVerificationBloc extends Bloc<IdentityVerificationEvent, IdentityVerificationState> {
  IdentityVerificationBloc({
    required UploadDocumentUseCase uploadDocumentUseCase,
  }) : _uploadDocumentUseCase = uploadDocumentUseCase,
       super(const IdentityVerificationInitial()) {
    on<UploadDocumentEvent>(_onUploadDocument);
    on<ResetIdentityVerificationEvent>(_onResetIdentityVerification);
  }

  final UploadDocumentUseCase _uploadDocumentUseCase;

  Future<void> _onUploadDocument(
    UploadDocumentEvent event,
    Emitter<IdentityVerificationState> emit,
  ) async {
    emit(const IdentityVerificationLoading());
    
    try {
      debugPrint('Starting document upload...');
      debugPrint('Front photo: ${event.frontPhoto.path}');
      debugPrint('Back photo: ${event.backPhoto.path}');
      debugPrint('Selfie photo: ${event.selfiePhoto.path}');
      debugPrint('Document type: ${event.documentType}');

      final result = await _uploadDocumentUseCase(
        DocumentUploadParams(
          frontPhoto: event.frontPhoto,
          backPhoto: event.backPhoto,
          selfiePhoto: event.selfiePhoto,
          documentType: event.documentType,
        ),
      );

      result.fold(
        (failure) {
          debugPrint('Document upload failed: ${failure.message}');
          emit(IdentityVerificationError(failure.message));
        },
        (response) {
          debugPrint('Document upload successful: $response');
          emit(IdentityVerificationSuccess(response));
        },
      );
    } catch (e) {
      debugPrint('Document upload exception: $e');
      emit(IdentityVerificationError('Failed to upload documents: ${e.toString()}'));
    }
  }

  Future<void> _onResetIdentityVerification(
    ResetIdentityVerificationEvent event,
    Emitter<IdentityVerificationState> emit,
  ) async {
    emit(const IdentityVerificationInitial());
  }
}
