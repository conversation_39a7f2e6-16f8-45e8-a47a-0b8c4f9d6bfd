import 'package:cbrs/core/res/media_res.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

PreferredSizeWidget CustomAppBar({
  required BuildContext context,
  String? title,
  Widget? titleWidget,
  bool automaticallyImplyLeading = true,
  Color backgroundColor = Colors.white,
  List<Widget>? actions,
  Widget? leading,
  bool centerTitle = false,
  bool hideLeading = false,
}) {
  return AppBar(
    automaticallyImplyLeading: automaticallyImplyLeading,
    elevation: 20,
    backgroundColor: Colors.white,
    surfaceTintColor: Colors.white,
    shadowColor: Colors.black.withOpacity(0.21),
    centerTitle: centerTitle,
    title: titleWidget ??
        (title != null
            ? Text(
                title,
                style: GoogleFonts.outfit(
                    fontWeight: FontWeight.w500, fontSize: 18.sp),
              )
            : null),
    actions: actions,
    leading: hideLeading
        ? null
        : leading ??
            IconButton(
              icon: const ImageIcon(
                AssetImage(MediaRes.backButtonIcon),
                size: 24,
              ),
              onPressed: () {
                Navigator.pop(context);
              },
            ),
  );
}
