import 'package:cbrs/core/common/widgets/confirm/custom_confirm_transaction_bottom_sheet.dart';
import 'package:cbrs/core/common/widgets/confirm/custom_otp_bottom_sheet.dart';
import 'package:cbrs/core/common/widgets/custom_pin_screen.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/common/widgets/success/custom_success_transaction_bottom_sheet.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transactions/domain/entities/confirm_transfer.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart'
    as tx_type;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class TransactionBottomSheetsManager {
  TransactionBottomSheetsManager({
    required this.context,
    required this.transactionType,
    required this.pinController,
    required this.onPinSubmitted,
    required this.onTransactionSuccess,
    required this.onTransactionComplete,
    this.reasonController,
    this.isFromChat = false,
    this.isFromMoneyRequest = false,
    this.onMoneyRequest,
  });
  final BuildContext context;
  final tx_type.TransactionType transactionType;
  final TextEditingController pinController;
  final TextEditingController? reasonController;

  final Function(String) onPinSubmitted;
  final Function(ConfirmTransferResponse) onTransactionSuccess;
  final Function() onTransactionComplete;
  final Function()? onMoneyRequest;

  final bool isFromChat;
  final bool isFromMoneyRequest;
  bool _isConfirmLoading = false;

  void showConfirmScreenBottomSheet({
    required Map<String, dynamic> data,
    required bool requiresOtp,
    required String billRefNo,
    required double billAmount,
    String status = 'Wait',
    String originalCurrency = '', //
    required double totalAmount, // TODO,

    String confirmButtonText = 'Confirm Transfer',
  }) {
    showModalBottomSheet<dynamic>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: !isFromMoneyRequest,
      isDismissible: !isFromMoneyRequest,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, updateState) {
            return SafeArea(
              bottom: true,
              child: Padding(
                padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom,
                ),
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    maxHeight: MediaQuery.of(context).size.height * 0.9,
                  ),
                  child: CustomConfirmTransactionBottomSheet(
                    data: data,
                    transactionType: transactionType.value,
                    confirmButtonText: confirmButtonText,
                    status: status,
                    billAmount: billAmount,
                    totalAmount: totalAmount,
                    originalCurrency: originalCurrency,
                    reasonRequestController: reasonController,
                    isLoading: _isConfirmLoading,
                    isFromMoneyRequest: isFromMoneyRequest,
                    onContinue: () {
                      debugPrint('money req uest');
                      if (isFromMoneyRequest) {
                        updateState(() => _isConfirmLoading = true);
                        onMoneyRequest!();
                        // Navigator.pop(context);
                        return;
                      } else {
                        if (Navigator.of(context).canPop()) {
                          Navigator.pop(context);
                        }
                        if (requiresOtp) {
                          showConfirmOtpScreenBottomSheet(billRefNo: billRefNo);
                        } else {
                          showConfirmPinScreenBottomSheet(billRefNo: billRefNo);
                        }
                      }
                    },
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  void showConfirmOtpScreenBottomSheet({required String billRefNo}) {
    final transactionBloc = context.read<TransactionBloc>();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.9,
            ),
            child: BlocProvider<TransactionBloc>.value(
              value: transactionBloc,
              child: CustomOtpBottomSheet(
                billRefNo: billRefNo,
                otpFor: transactionType.value,
                transactionType: transactionType,
                focusNode: FocusNode(),
                onOtpVerified: (otpCode) {
                  Navigator.pop(context);
                  showConfirmPinScreenBottomSheet(billRefNo: billRefNo);
                },
                onResendSuccess: () {
                  // Optional: actions after OTP is resent
                },
              ),
            ),
          ),
        );
      },
    );
  }

  bool _isCheckingPin = false;

  void showConfirmPinScreenBottomSheet({required String billRefNo}) {
    final transactionBloc = context.read<TransactionBloc>();

    showModalBottomSheet<dynamic>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setModalState) {
            return BlocProvider.value(
              value: transactionBloc,
              child: BlocConsumer<TransactionBloc, TransactionState>(
                listener: (context, state) {
                  if (state is PinErrorState) {
                    debugPrint('hello');
                    CustomToastification(
                      context,
                      message: state.message,
                    );
                    resetPinState(setModalState);
                  } else if (state is ConfirmTransferSuccess) {
                    setModalState(() {});
                    onTransactionSuccess(state.transaction);
                  }
                },
                builder: (context, state) {
                  return CustomPinScreen(
                    controller: pinController,
                    onChanged: (keys, isKey) {
                      if (_isCheckingPin) return;

                      setModalState(() {
                        if (!isKey) {
                          pinController.text = pinController.text.isNotEmpty
                              ? pinController.text
                                  .substring(0, pinController.text.length - 1)
                              : '';
                          pinController.selection = TextSelection.fromPosition(
                            TextPosition(offset: pinController.text.length),
                          );
                        }
                        pinController.text = "${pinController.text}$keys";
                        pinController.selection = TextSelection.fromPosition(
                          TextPosition(offset: pinController.text.length),
                        );
                      });
                    },
                    onSubmitted: (pin) {
                      if (_isCheckingPin) return;

                      setModalState(() => _isCheckingPin = true);
                      onPinSubmitted(pin);
                    },
                    isLoading: _isCheckingPin,
                  );
                },
              ),
            );
          },
        );
      },
    );
  }

  Future<void> resetPinState(StateSetter setModalState) async {
    setModalState(() {
      _isCheckingPin = false;
      pinController.clear();
    });
  }

  void showSuccessScreenBottomSheet(
    Map<String, dynamic> data, {
    String status = 'Wait',
    String originalCurrency = '', //
    required double totalAmount, // TODO,
    required double billAmount, // TODO,

    String title = '', //
    bool showActionButtons = true,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      isDismissible: false,
      enableDrag: false,
      builder: (context) {
        return CustomSuccessTransactionBottomSheet(
          data: data,
          originalCurrency: originalCurrency,
          totalAmount: totalAmount,
          billAmount: billAmount,
          status: status,
          title: title,
          transactionType: transactionType.value,
          onContinue: onTransactionComplete,
          isFromChat: isFromChat,
          showActionButtons: showActionButtons,
        );
      },
    );
  }
}
