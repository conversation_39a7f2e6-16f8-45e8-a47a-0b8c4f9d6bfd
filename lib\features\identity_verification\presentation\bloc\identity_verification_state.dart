import 'package:equatable/equatable.dart';
import 'package:cbrs/features/identity_verification/domain/entities/document_upload_response.dart';

abstract class IdentityVerificationState extends Equatable {
  const IdentityVerificationState();

  @override
  List<Object?> get props => [];
}

class IdentityVerificationInitial extends IdentityVerificationState {
  const IdentityVerificationInitial();
}

class IdentityVerificationLoading extends IdentityVerificationState {
  const IdentityVerificationLoading();
}

class IdentityVerificationSuccess extends IdentityVerificationState {
  const IdentityVerificationSuccess(this.response);

  final DocumentUploadResponse response;

  @override
  List<Object?> get props => [response];
}

class IdentityVerificationError extends IdentityVerificationState {
  const IdentityVerificationError(this.message);

  final String message;

  @override
  List<Object?> get props => [message];
}
