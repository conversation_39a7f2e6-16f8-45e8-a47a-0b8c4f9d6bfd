import 'package:cbrs/core/common/controllers/currency_input_controller.dart';
import 'package:cbrs/core/common/widgets/confirm/custom_otp_bottom_sheet.dart';
import 'package:cbrs/core/common/widgets/currency_input_widget.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_cached_image.dart';
import 'package:cbrs/core/common/widgets/custom_connect_loader.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/common/widgets/transaction_bottom_sheets_manager.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/add_money/application/bloc/add_money_bloc.dart';
import 'package:cbrs/features/add_money/domain/entities/linked_account.dart';
import 'package:cbrs/features/add_money/presentation/widgets/account_selector.dart';
import 'package:cbrs/features/add_money/presentation/widgets/no_linked_account_widget.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transactions/domain/entities/confirm_transfer.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart'
    as tx_type;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class AddMoneyScreen extends StatefulWidget {
  const AddMoneyScreen({
    super.key,
    this.defaultBankId,
    this.defaultAccountNumber,
  });
  final String? defaultBankId;
  final String? defaultAccountNumber;

  @override
  State<AddMoneyScreen> createState() => _AddMoneyScreenState();
}

// Custom controller that prevents keyboard input when no account is selected
class AddMoneyCurrencyController extends CurrencyInputController {
  AddMoneyCurrencyController({
    required this.context,
    required this.hasAccountSelected,
    required super.currencyType,
    required double maxAmount,
    super.minTransferLimit,
    super.ignoreWalletAmountCheck,
  }) : super(
          maxBalance: maxAmount,
        );
  final BuildContext context;
  final bool Function() hasAccountSelected;

  @override
  void onKeyPressed(String value) {
    if (!hasAccountSelected()) {
      CustomToastification(
        context,
        message: 'Please select an account first',
      );
      return;
    }
    super.onKeyPressed(value);
  }
}

class _AddMoneyScreenState extends State<AddMoneyScreen> {
  LinkedAccount? _selectedAccount;
  bool isLoading = false;
  // late AddMoneyCurrencyController _currencyController;
  final TextEditingController _pinController = TextEditingController();
  late TransactionBottomSheetsManager _bottomSheetsManager;
  String _currentBillRefNo = '';
  late CurrencyInputController _currencyController;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    context.read<AddMoneyBloc>().add(
          const GetLinkedAccountsEvent(
            page: 1,
            limit: 20,
          ),
        );

    _currencyController = CurrencyInputController(
      currencyType: CurrencyType.etb,
      maxBalance: 0,
      ignoreWalletAmountCheck: true,
    );

    _bottomSheetsManager = TransactionBottomSheetsManager(
      context: context,
      transactionType: tx_type.TransactionType.addMoney,
      pinController: _pinController,
      onPinSubmitted: (pin) {
        // Check if we have an OTP verification state
        final currentState = context.read<AddMoneyBloc>().state;

        if (currentState is AddMoneyOtpVerified) {
          // If OTP was verified, use it with the PIN
          context.read<TransactionBloc>().add(
                ConfirmTransferEvent(
                  pin: pin,
                  billRefNo: _currentBillRefNo,
                  transactionType: tx_type.TransactionType.addMoney,
                ),
              );
        } else {
          // Use the stored billRefNo instead of trying to get it from the state
          if (_currentBillRefNo.isNotEmpty) {
            context.read<TransactionBloc>().add(
                  ConfirmTransferEvent(
                    pin: pin,
                    billRefNo: _currentBillRefNo,
                    transactionType: tx_type.TransactionType.addMoney,
                  ),
                );
          } else {
            // Handle the case where billRefNo is not available
            CustomToastification(
              context,
              message: 'Something went wrong. Please try again.',
            );
          }
        }
      },
      onTransactionSuccess: (response) {
        Navigator.pop(context);
        _showSuccessScreenBottomSheet(response);
      },
      onTransactionComplete: () {
        Navigator.pop(context);
        context.go(AppRouteName.home);
      },
    );
  }

  @override
  void dispose() {
    _pinController.dispose();
    super.dispose();
  }

  void _showConfirmScreenBottomSheet({
    required String senderName,
    required String billRefNo,
    required bool requiresOtp,
  }) {
    /*    'Transaction': 'Add Money',
                'SenderName': senderName,
                'bankName': _selectedAccount!.bank.name,
                'accountNumber': _selectedAccount!.accountNumber,
                'amount': _currencyController.numericAmount,
                'originalCurrency': 'ETB',
                'serviceCharge': 0.0,
                'VAT': 0.0,
                'createdAt': DateTime.now().toString(),
                'totalAmount': _currencyController.numericAmount,
                'billRefNo': state.billRefNo,
                'requiresOtp': state.requiresOtp,
                */

    final data = {
      'Sender Name': senderName,
      'Bank Name': _selectedAccount!.bank.name,
      'Account Number': _selectedAccount!.accountNumber,
      'Amount': _currencyController.numericAmount,
      'serviceCharge': 0.0,
      'VAT': 0.0,
      'Date': AppMapper.safeFormattedDate(DateTime.now().toString()),
      'billRefNo': billRefNo,
    };
    // Store the billRefNo for later use
    _currentBillRefNo = data['billRefNo'] as String;

    // Check if OTP is required from the state
    final currentState = context.read<AddMoneyBloc>().state;
    final requiresOtp =
        currentState is AddMoneyPinRequired ? currentState.requiresOtp : false;

    if (requiresOtp) {
      // If OTP is required, show the OTP bottom sheet directly
      showModalBottomSheet<void>(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.9,
              ),
              child: CustomOtpBottomSheet(
                billRefNo: _currentBillRefNo,
                otpFor: 'add_money',
                focusNode: FocusNode(),
                transactionType: tx_type.TransactionType.addMoney,
                onOtpVerified: (String otpCode) {
                  // When OTP is verified, close the OTP sheet and show PIN screen
                  Navigator.pop(context);

                  // Verify OTP using TransactionBloc
                  context.read<TransactionBloc>().add(
                        VerifyOtpEvent(
                          billRefNo: _currentBillRefNo,
                          otpFor: 'add_money',
                          otpCode: int.parse(otpCode),
                        ),
                      );
                },
                onResendSuccess: () {
                  // Optional: actions after OTP is resent
                },
              ),
            ),
          );
        },
      );
    } else {
      // If OTP is not required, show the regular confirm screen
      _bottomSheetsManager.showConfirmScreenBottomSheet(
        data: data,
        totalAmount: _currencyController.numericAmount,
        billAmount: _currencyController.numericAmount,
        confirmButtonText: 'Confirm Add Money',
        requiresOtp: false,
        billRefNo: _currentBillRefNo,
        originalCurrency: 'ETB',
      );
    }
  }

  void _showSuccessScreenBottomSheet(ConfirmTransferResponse response) {
    final transaction = response.transaction;
    _bottomSheetsManager.showSuccessScreenBottomSheet(
      totalAmount: transaction.totalAmount,
      billAmount: transaction.totalAmount,
      originalCurrency: 'ETB',
      status: transaction.status,
      {
        'Sender Name': transaction.senderName,
        'Account Number': transaction.beneficiaryAccountNo,
        'Bank Name': transaction.bankName,
        'BillRefNo': transaction.billRefNo,
        'Date': AppMapper.safeFormattedDate(transaction.createdAt),
        // 'serviceCharge': transaction.serviceCharge,
        // 'VAT': transaction.vat,
        'Amount in ETB': transaction.billAmount,
        'FT Number': transaction.walletFTNumber,
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Money'),
      ),
      body: SafeArea(
        child: Container(
          child: BlocConsumer<AddMoneyBloc, AddMoneyState>(
            listener: (context, state) {
              // after check transfer rules - this should be hit
              if (state is AddMoneyAmountValidated) {
                // Get sender name
                final senderName = '${_selectedAccount?.member.firstName} '
                    '${_selectedAccount?.member.middleName} '
                    '${_selectedAccount?.member.lastName}';

                // Initiate add money transfer
                context.read<AddMoneyBloc>().add(
                      AddMoneyTransferEvent(
                        accountNumber: _selectedAccount!.accountNumber,
                        bankId: _selectedAccount!.bank.id,
                        amount: _currencyController.numericAmount,
                        currency: 'ETB',
                        senderName: senderName,
                      ),
                    );
              } else if (state is AddMoneyPinRequired) {
                setState(() => isLoading = false);

                final senderName = '${_selectedAccount?.member.firstName} '
                    '${_selectedAccount?.member.middleName} '
                    '${_selectedAccount?.member.lastName}';

                _showConfirmScreenBottomSheet(
                  senderName: senderName,
                  billRefNo: state.billRefNo,
                  requiresOtp: state.requiresOtp,

                  /*{
                  'Transaction': 'Add Money',
                  'SenderName': senderName,
                  'bankName': _selectedAccount!.bank.name,
                  'accountNumber': _selectedAccount!.accountNumber,
                  'amount': _currencyController.numericAmount,
                  'originalCurrency': 'ETB',
                  'serviceCharge': 0.0,
                  'VAT': 0.0,
                  'createdAt': DateTime.now().toString(),
                  'totalAmount': _currencyController.numericAmount,
                  'billRefNo': state.billRefNo,
                  'requiresOtp': state.requiresOtp,
                }*/
                );
              }
              // if (state is EmptyLinkedAccountState) {
              //   CustomToastification(context, message: 'EmptyLinkedAccountState');
              // }
            },
            builder: (context, state) {
              if (state is AddMoneyLoading && _selectedAccount == null) {
                return const CustomConnectLoader();
              }
              if (state is EmptyLinkedAccountState) {
                return const NoLinkedAccountWidget();
              }
              return CurrencyInputWidget(
                controller: _currencyController,
                title: '',
                subtitle: '',
                transactionType: 'add_money',
                balanceLabel: _selectedAccount != null
                    ? _selectedAccount!.accountBalance.toString()
                    : '0.0',
                walletBalanceLabel: 'Account Balance:',
                header: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 16.w,
                    vertical: 16.h,
                  ),
                  child: Column(
                    children: [
                      const CustomPageHeader(
                        pageTitle: 'Add Money to you Wallet',
                        description:
                            'Select a linked account, top up your wallet, and get it ready for transfers.',
                      ),
                      const SizedBox(
                        height: 16,
                      ),
                      _selectAccount(),
                    ],
                  ),
                ),
                isLoading: isLoading,
                onContinue: () {
                  if (_selectedAccount == null) {
                    CustomToastification(
                      context,
                      message: 'Please select an account first',
                    );
                    return;
                  }

                  setState(() {
                    isLoading = true;
                  });

                  // Get the amount from the controller
                  final amount = _currencyController.numericAmount;

                  // First validate the amount
                  context.read<AddMoneyBloc>().add(
                        ValidateAddMoneyAmountEvent(
                          amount: amount,
                          currency: 'ETB',
                          bankId: _selectedAccount!.bank.id,
                          accountNumber: _selectedAccount!.accountNumber,
                        ),
                      );
                },
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _selectAccount() {
    return GestureDetector(
      onTap: _showSelectAccountModal,
      child: Container(
        padding: EdgeInsets.only(
          left: _selectedAccount != null ? 8.w : 12.w,
          right: _selectedAccount != null ? 15.w : 12.w,
          top: _selectedAccount != null ? 8.h : 15.h,
          bottom: _selectedAccount != null ? 8.h : 15.h,
        ),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Row(
          children: [
            if (_selectedAccount != null)
              Container(
                height: 40.h,
                width: 40.w,
                margin: EdgeInsets.only(right: 8.w),
                padding: const EdgeInsets.all(4),
                child: CustomCachedImage(
                  url: _selectedAccount!.bank.logo,
                ),
              ),
            Expanded(
              child: _selectedAccount != null
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomBuildText(
                          text: _selectedAccount != null
                              ? '${_selectedAccount!.member.firstName} '
                                  '${_selectedAccount!.member.middleName} '
                                  '${_selectedAccount!.member.lastName}'
                              : '',
                          style: GoogleFonts.outfit(
                            fontWeight: FontWeight.w400,
                            fontSize: 13.sp,
                            color: Colors.black,
                          ),
                        ),
                        Text(
                          _selectedAccount!.accountNumber,
                          style: GoogleFonts.outfit(
                            fontSize: 13.sp,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    )
                  : const CustomBuildText(
                      text: 'Select Account',
                    ),
            ),
            Icon(
              Icons.keyboard_arrow_down,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }

  void _showSelectAccountModal() {
    showModalBottomSheet<void>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => BlocProvider<AddMoneyBloc>(
        create: (context) => sl<AddMoneyBloc>(),
        child: Container(
          height: MediaQuery.of(context).size.height * 0.7,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(24.r),
            ),
          ),
          child: AccountSelector(
            selectedAccount: _selectedAccount,
            onAccountSelected: (account) {
              setState(() {
                _selectedAccount = account;

                // Get current transfer limits from the TransactionBloc
                final transactionState = context.read<TransactionBloc>().state;
                var minLimit = 0.0; // Default minimum

                // Extract limits from TransactionBloc if available
                if (transactionState is TransferLimitLoaded) {
                  minLimit = transactionState.transferLimit.minTransferLimit;
                }

                _currencyController = AddMoneyCurrencyController(
                  context: context,
                  hasAccountSelected: () => _selectedAccount != null,
                  currencyType: CurrencyType.etb,
                  maxAmount: account.accountBalance,
                  minTransferLimit: minLimit,
                );
              });
            },
          ),
        ),
      ),
    );
  }
}
