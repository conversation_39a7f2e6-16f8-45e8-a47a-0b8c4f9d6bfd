import 'package:flutter/material.dart';

class TextScaling {
  static double getScaleFactor(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width <= 320) return 0.8;  
    if (width <= 375) return 1.0;  
    if (width <= 414) return 1.1;  
    if (width <= 768) return 1.3; 
    return 1.5;                   
  }

  static double getResponsiveSize(BuildContext context, double baseSize) {
    return baseSize * getScaleFactor(context);
  }
}