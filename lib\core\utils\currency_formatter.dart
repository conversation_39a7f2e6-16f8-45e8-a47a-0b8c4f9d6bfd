import 'package:intl/intl.dart';

class CurrencyFormatter {
  static String formatBalance({
    required double amount,
    String currency = 'USD',
    bool showSymbol = true,
    int decimalPlaces = 2,
  }) {
    // Handle negative amounts
    final isNegative = amount < 0;
    final absoluteAmount = amount.abs();

    // Create number formatter
    final formatter = NumberFormat.currency(
      locale: 'en_US',
      symbol: '',
      decimalDigits: decimalPlaces,
    );

    // Format the number
    String formattedAmount = formatter.format(absoluteAmount);

    // Add currency symbol/code based on currency type
    String currencyIdentifier = '';
    if (showSymbol) {
      switch (currency.toUpperCase()) {
        case 'USD':
          currencyIdentifier = '\$';
          break;
        case 'ETB':
          currencyIdentifier = 'ETB ';
          break;
        default:
          currencyIdentifier = '$currency ';
      }
    }

    // Combine parts
    final result = isNegative ? '-$currencyIdentifier$formattedAmount' : '$currencyIdentifier$formattedAmount';

    // Handle ETB placement
    if (currency.toUpperCase() == 'ETB' && showSymbol) {
      return '$formattedAmount ETB';
    }

    return result;
  }

  // Helper for wallet balances specifically
  static String formatWalletBalance(double balance, String currency) {
    return formatBalance(
      amount: balance,
      currency: currency,
      showSymbol: true,
      decimalPlaces: 2,
    );
  }

  static String formatNumber(double number) {
    final formatter = NumberFormat('#,##0.00', 'en_US');
    return formatter.format(number);
  }

  static bool isValidAmount(String amount) {
    try {
      final cleanAmount = amount.replaceAll(RegExp(r'[^\d.]'), '');
      final wholePart = cleanAmount.contains('.') 
          ? cleanAmount.split('.')[0] 
          : cleanAmount;
          
      // Check if whole number part exceeds 12 digits
      if (wholePart.length > 12) {
        return false;
      }

      final number = double.parse(cleanAmount);
      return number >= 0 &&
             cleanAmount.split('.').length <= 2 && // Only one decimal point
             (cleanAmount.contains('.') ? 
               cleanAmount.split('.')[1].length <= 2 : true); // Max 2 decimal places
    } catch (e) {
      return false;
    }
  }

  static String formatWalletAmount({
    required double amount,
    required String currency,
    bool showSymbol = true,
    bool compact = false,
  }) {
    // Handle negative amounts
    final isNegative = amount < 0;
    final absoluteAmount = amount.abs();

    // Format large numbers
    String formattedAmount;
    if (compact && absoluteAmount >= 1000) {
      if (absoluteAmount >= 1000000) {
        formattedAmount = '${(absoluteAmount / 1000000).toStringAsFixed(1)}M';
      } else {
        formattedAmount = '${(absoluteAmount / 1000).toStringAsFixed(1)}K';
      }
    } else {
      // Add thousand separators and fixed decimal places
      final parts = absoluteAmount.toStringAsFixed(2).split('.');
      final wholePart = parts[0].replaceAllMapped(
        RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
        (match) => '${match[1]},',
      );
      formattedAmount = '$wholePart.${parts[1]}';
    }

    // Add currency symbol/code based on currency type
    String currencyIdentifier = '';
    if (showSymbol) {
      switch (currency.toUpperCase()) {
        case 'USD':
          currencyIdentifier = '\$';
          break;
        case 'ETB':
          currencyIdentifier = 'ETB ';
          break;
        default:
          currencyIdentifier = '$currency ';
      }
    }

    // Combine parts with appropriate placement
    if (currency.toUpperCase() == 'ETB' && showSymbol) {
      return isNegative 
          ? '-$formattedAmount ETB'
          : '$formattedAmount ETB';
    }

    return isNegative 
        ? '-$currencyIdentifier$formattedAmount'
        : '$currencyIdentifier$formattedAmount';
  }

  static String getReadableAmount({
    required double amount,
    required String currency,
    bool showSymbol = true,
    bool compact = false,
  }) {
    if (amount == 0) {
      return currency.toUpperCase() == 'ETB' ? '0.00 ETB' : '\$0.00';
    }

    return formatWalletAmount(
      amount: amount,
      currency: currency,
      showSymbol: showSymbol,
      compact: compact,
    );
  }
} 
