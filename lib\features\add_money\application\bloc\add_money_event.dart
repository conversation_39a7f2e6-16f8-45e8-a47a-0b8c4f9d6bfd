part of 'add_money_bloc.dart';

abstract class AddMoneyEvent extends Equatable {
  const AddMoneyEvent();

  @override
  List<Object?> get props => [];
}

class GetLinkedAccountsEvent extends AddMoneyEvent {
  const GetLinkedAccountsEvent({
    required this.page,
    required this.limit,
    this.status = 'LINKED',
  });
  final int page;
  final int limit;
  final String status;

  @override
  List<Object?> get props => [page, limit, status];
}

class CheckAccountBalanceEvent extends AddMoneyEvent {
  const CheckAccountBalanceEvent({
    required this.bankId,
    required this.accountNumber,
  });
  final String bankId;
  final String accountNumber;

  @override
  List<Object?> get props => [bankId, accountNumber];
}

class ValidateAddMoneyAmountEvent extends AddMoneyEvent {
  const ValidateAddMoneyAmountEvent({
    required this.amount,
    required this.currency,
    required this.bankId,
    required this.accountNumber,
  });
  final double amount;
  final String currency;
  final String bankId;
  final String accountNumber;

  @override
  List<Object?> get props => [amount, currency];
}

class AddMoneyTransferEvent extends AddMoneyEvent {
  const AddMoneyTransferEvent({
    required this.accountNumber,
    required this.bankId,
    required this.amount,
    required this.currency,
    required this.senderName,
  });
  final String accountNumber;
  final String bankId;
  final double amount;
  final String currency;
  final String senderName;

  @override
  List<Object?> get props => [
        accountNumber,
        bankId,
        amount,
        currency,
        senderName,
      ];
}

class SubmitAddMoneyPinEvent extends AddMoneyEvent {
  const SubmitAddMoneyPinEvent({
    required this.pin,
    required this.billRefNo,
    this.otp,
  });
  final String pin;
  final String billRefNo;
  final String? otp;

  @override
  List<Object?> get props => [pin, billRefNo, otp];
}

class ResendAddMoneyOtpEvent extends AddMoneyEvent {
  const ResendAddMoneyOtpEvent({
    required this.billRefNo,
    required this.otpFor,
  });
  final String billRefNo;
  final String otpFor;

  @override
  List<Object?> get props => [billRefNo, otpFor];
}

class VerifyAddMoneyOtpEvent extends AddMoneyEvent {
  const VerifyAddMoneyOtpEvent({
    required this.billRefNo,
    required this.otpFor,
    required this.otpCode,
  });
  final String billRefNo;
  final String otpFor;
  final int otpCode;

  @override
  List<Object?> get props => [billRefNo, otpFor, otpCode];
}
