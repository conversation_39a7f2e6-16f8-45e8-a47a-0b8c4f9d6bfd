part of 'auth_bloc.dart';

abstract class AuthState extends Equatable {
  const AuthState();

  bool get isAuthenticated => this is LoggedInWithPinState;

  @override
  List<Object?> get props => [];
}

class AuthInitial extends AuthState {
  const AuthInitial();
}

class AuthLoading extends AuthState {
  const AuthLoading();
}

class AuthError extends AuthState {
  const AuthError(this.message);

  final String message;

  @override
  List<Object?> get props => [message];
}

class EmailVerificationSentState extends AuthState {
  final ResponseHandler response;

  const EmailVerificationSentState(
    this.response,
  );
  @override
  List<Object?> get props => [response];
}

class OtpSentState extends AuthState {
  final ResponseHandler response;

  const OtpSentState(
    this.response,
  );
  @override
  List<Object?> get props => [response];
}

class OtpVerifiedState extends AuthState {
    final ResponseHandler response;

  const OtpVerifiedState(
    this.response,
  );
  @override
  List<Object?> get props => [response];
}

class EmailVerifiedState extends AuthState {
  final ResponseHandler response;

  const EmailVerifiedState(
    this.response,
  );
  @override
  List<Object?> get props => [response];
}

class SignedUpState extends AuthState {
  final String? email;
  final String? phoneNumber;
  final int? otp;
  final bool success;
  final String message;

  const SignedUpState({
    this.email,
    this.phoneNumber,
    this.otp,
    this.success = true,
    this.message = 'Signup successful',
  });

  @override
  List<Object?> get props => [email, phoneNumber, otp, success, message];
}

class PinCreatedState extends AuthState {
  const PinCreatedState();
}

class LoggedInWithPinState extends AuthState {
  const LoggedInWithPinState(this.user);

  final LocalUser user;

  @override
  List<Object?> get props => [user];
}

class PinResetRequestedState extends AuthState {
  final ResponseHandler response;

  const PinResetRequestedState(
    this.response,
  );
  @override
  List<Object?> get props => [response];
}

class PinResetState extends AuthState {
  const PinResetState();
}

class DeviceUnlinkedState extends AuthState {
  final ResponseHandler response;

  const DeviceUnlinkedState(
    this.response,
  );
  @override
  List<Object?> get props => [response];
}

class LoggedOutState extends AuthState {
  const LoggedOutState();
}

class BiometricAuthenticatedState extends AuthState {
  const BiometricAuthenticatedState();
}

class TokenExpiredState extends AuthState {
  final LocalUser user;

  const TokenExpiredState(this.user);

  @override
  List<Object?> get props => [user];
}
