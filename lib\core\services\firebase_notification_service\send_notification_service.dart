import 'dart:convert';
import 'package:cbrs/core/services/firebase_notification_service/fcm_service.dart';
import 'package:cbrs/core/services/firebase_notification_service/get_service_key.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:flutter/cupertino.dart';
import 'package:http/http.dart' as http;

class SendNotificationService {
  Future<void> sendVideoCallNotification({
    required String senderId,
    required String chatRoomId,
    required String receiverId,
    required String receiverAvatar,
    required String senderLastName,
    required String senderFirstName,
    required String receiverLastName,
    required String receiverFirstName,
  }) async {
    final fcmService = sl<FcmService>();
    final receiverFcmToken = await fcmService.getFcmToken(receiverId);
    final senderFCMToken = await fcmService.getFcmToken(senderId);

    if (receiverFcmToken == null) {
      throw Exception('FCM token not found for receiver: $receiverFcmToken');
    }

    try {
      await sendNotificationUsing<PERSON>pi(
        token: receiverFcmToken,
        title: '',
        body: ''
            '$receiverLastName',
        data: {
          'chatRoomId': chatRoomId,
          'senderId': receiverId,
          'receiverId': senderId,
          'senderFCMToken': senderFCMToken,
          'receiverFirstName': receiverFirstName,
          'receiverLastName': receiverLastName,
          'receiverAvatar': receiverAvatar,
          'senderFirstName': senderFirstName,
          'senderLastName': senderLastName,
          'isIncoming': 'true',
          'notificationType': 'VIDEO_CALL',
        },
        isSilentNotification: true,
      );
    } catch (e) {
      throw Exception('Failed to send video call notification: $e');
    }
  }

  Future<void> sendVideoCallRejectedNotification({
    required String fcmToken,
  }) async {
    try {
      if (fcmToken.isEmpty) {
        throw Exception('FCM token not found for sender');
      }

      await sendNotificationUsingApi(
        token: fcmToken,
        title: '',
        body: '',
        data: {
          'notificationType': 'VIDEO_CALL_REJECTED',
        },
        isSilentNotification: true,
      );
    } catch (e) {
      throw Exception('Failed to send video call notification: $e');
    }
  }

  Future<void> sendNotificationUsingApi({
    required String token,
    required String title,
    required String body,
    required bool isSilentNotification,
    Map<String, dynamic>? data,
  }) async {
    final serverKey = await GetServerKey.getServerKey();
    const url =
        'https://fcm.googleapis.com/v1/projects/cbrs-763a5/messages:send';

    final message = <String, dynamic>{
      'message': {
        'token': token,
        if (!isSilentNotification)
          'notification': {
            'title': title,
            'body': body,
          },
        'data': data ?? {},
        'apns': {
          'payload': {
            'aps': {
              if (isSilentNotification) 'content-available': 1,
            },
          },
        },
        'android': {
          'priority': isSilentNotification ? 'NORMAL' : 'HIGH',
        },
      },
    };

    final response = await http.post(
      Uri.parse(url),
      headers: {
        'Authorization': 'Bearer $serverKey',
        'Content-Type': 'application/json',
      },
      body: jsonEncode(message),
    );

    if (response.statusCode == 200) {
      debugPrint('Notification sent successfully');
    } else {
      final errorResponse = jsonDecode(response.body);
      debugPrint(
        'Failed to send notification. Status: ${response.statusCode}, Error: '
        '${errorResponse['error']['message']} body :  $errorResponse',
      );
    }
  }
}
