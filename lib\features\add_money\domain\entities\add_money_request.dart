import 'package:equatable/equatable.dart';

class AddMoneyRequest extends Equatable {
  final String beneficiaryAccountNo;
  final String bankId;
  final double amount;
  final String currency;
  final String transactionType;

  const AddMoneyRequest({
    required this.beneficiaryAccountNo,
    required this.bankId,
    required this.amount,
    required this.currency,
    this.transactionType = 'add_money',
  });

  @override
  List<Object?> get props => [
        beneficiaryAccountNo,
        bankId,
        amount,
        currency,
        transactionType,
      ];
}