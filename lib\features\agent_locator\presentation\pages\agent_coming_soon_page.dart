import 'package:cbrs/core/common/widgets/custom_coming_soon.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cbrs/features/donations/presentations/widgets/donation_header.dart';
import 'package:cbrs/features/donations/presentations/widgets/donation_tabs.dart';
import 'package:cbrs/features/donations/presentations/widgets/donation_list.dart';

class AgentLocatorComingSoonScreen extends StatelessWidget {
  const AgentLocatorComingSoonScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Agent Locator',
          style: GoogleFonts.outfit(
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: const CustomComingSoon(
        message: 'Agent Locater is coming soon! Stay tuned for the update.',
        cardIcon: MediaRes.agentLocator,
      ),

      //const _DonationsView(),
    );
  }
}

class _DonationsView extends StatefulWidget {
  const _DonationsView({super.key});

  @override
  State<_DonationsView> createState() => _DonationsViewState();
}

class _DonationsViewState extends State<_DonationsView> {
  int _selectedTabIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const DonationHeader(),
        const SizedBox(height: 16),
        DonationTabs(
          selectedIndex: _selectedTabIndex,
          onTabChanged: (index) {
            setState(() {
              _selectedTabIndex = index;
            });
          },
        ),
        const SizedBox(height: 16),
        Expanded(
          child: DonationList(
            tabIndex: _selectedTabIndex,
          ),
        ),
      ],
    );
  }
}
