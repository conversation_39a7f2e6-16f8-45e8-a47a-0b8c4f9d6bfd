import 'package:flutter/material.dart';

class GetAppTheme {
  static final ThemeData birrTheme = ThemeData(
    primaryColor: Colors.blue, // Main color for Birr Theme
    scaffoldBackgroundColor: Colors.white,
    appBarTheme: const AppBarTheme(backgroundColor: Colors.blue),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
    ),
  );

  static final ThemeData dollarTheme = ThemeData(
    primaryColor: Colors.green, // Main color for Dollar Theme
    scaffoldBackgroundColor: Colors.white,
    appBarTheme: const AppBarTheme(backgroundColor: Colors.green),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
    ),
  );
}
