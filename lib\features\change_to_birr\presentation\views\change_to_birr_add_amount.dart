import 'package:cbrs/core/common/controllers/currency_input_controller.dart';
import 'package:cbrs/core/common/widgets/confirm/custom_confirm_transaction_bottom_sheet.dart';
import 'package:cbrs/core/common/widgets/confirm/custom_otp_bottom_sheet.dart';
import 'package:cbrs/core/common/widgets/currency_input_widget.dart';
import 'package:cbrs/core/common/widgets/custom_connect_loader.dart';
import 'package:cbrs/core/common/widgets/custom_pin_screen.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/common/widgets/success/custom_success_transaction_bottom_sheet.dart';
import 'package:cbrs/core/common/widgets/transaction_bottom_sheets_manager.dart';

import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/core/utils/currency_formatter.dart';
import 'package:cbrs/features/change_to_birr/application/bloc/change_to_birr_bloc.dart';
import 'package:cbrs/features/change_to_birr/data/models/change_to_birr_response.dart';
import 'package:cbrs/features/exchange_rate/application/bloc/exchange_rate_bloc/exchange_rate_bloc.dart';
import 'package:cbrs/features/exchange_rate/application/bloc/exchange_rate_bloc/exchange_rate_event.dart';
import 'package:cbrs/features/exchange_rate/application/bloc/exchange_rate_bloc/exchange_rate_state.dart';
import 'package:cbrs/features/exchange_rate/data/dto/exchange_rate_dto.dart';
import 'package:cbrs/features/link_acccount/presentation/views/index.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transactions/domain/entities/confirm_transfer.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart'
    as tx_type;
import 'package:cbrs/features/user/domain/entities/user.dart';
import 'package:cbrs/features/user/presentation/bloc/user_bloc.dart';
import 'package:cbrs/features/user/presentation/bloc/user_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ChangeToBirrAddAmount extends StatefulWidget {
  const ChangeToBirrAddAmount({super.key});

  @override
  State<ChangeToBirrAddAmount> createState() => _ChangeToBirrPageState();
}

class _ChangeToBirrPageState extends State<ChangeToBirrAddAmount> {
  late CurrencyInputController _currencyController;
  late TransactionBottomSheetsManager _bottomSheetsManager;

  bool _isLoading = false;

  bool _isCheckingPin = false;
  final TextEditingController _pinController = TextEditingController();

  bool ignoreWallletCheck = true;
  @override
  void initState() {
    super.initState();
    // _getWalletBalance(context);
    _currencyController = CurrencyInputController(
      currencyType: CurrencyType.usd,
      maxBalance: 0,
      ignoreWalletAmountCheck: ignoreWallletCheck,
    );
    context.read<ExchangeRateBloc>().add(FetchExchangeRates());
    _bottomSheetsManager = TransactionBottomSheetsManager(
      context: context,
      transactionType: tx_type.TransactionType.changeToBirr,
      pinController: _pinController,
      onPinSubmitted: (pin) {
        context.read<TransactionBloc>().add(
              ConfirmTransferEvent(
                pin: pin,
                billRefNo: (context.read<ChangeToBirrBloc>().state
                        as ConfirmChangeToBirrState)
                    .response
                    .billRefNo,
                transactionType: tx_type.TransactionType.changeToBirr,
              ),
            );
      },
      onTransactionSuccess: (response) {
        Navigator.pop(context);
        _showSuccessScreenBottomSheet(response);
      },
      onTransactionComplete: () {
        Navigator.pop(context);
        context.go(AppRouteName.home);
      },
    );
  }

  @override
  void dispose() {
    _currencyController.dispose();
    super.dispose();
  }

  void _onContinuePressed() {
    debugPrint('somethidn is checking here');

    try {
      final amount = _currencyController.numericAmount;

      setState(() {
        _isLoading = true;
      });
      context.read<ChangeToBirrBloc>().add(CheckTransferRulesRequested(amount));
    } catch (e) {
      CustomToastification(
        context,
        message: 'Invalid Amount',
      );
    }
  }

  void _showConfirmScreenBottomSheet(
    ChangeToBirrData response,
    bool requiresOtp,
  ) {
    final state = context.read<ChangeToBirrBloc>().state;

    debugPrint('response.originalCurrency: ${response.originalCurrency}');
    _bottomSheetsManager.showConfirmScreenBottomSheet(
      data: {
        'Transaction Type': 'Change to Birr',
        'Amount In USD': '${response.billAmount} USD',
        'Exchange Rate': '1\$ = ${response.exchangeRate} ETB',
        'Amount In ETB': '${response.paidAmount} ETB',
        'Service Charge':
            '${response.serviceCharge} ${response.originalCurrency}',
        'VAT': '${response.vat} ${response.originalCurrency}',
        'Date': AppMapper.safeFormattedDate(response.createdAt),
        'billRefNo': response.billRefNo,
      },
      originalCurrency: response.originalCurrency,
      totalAmount: response.totalAmount,
      billAmount: response.billAmount,
      status: 'Unpaid',
      confirmButtonText: 'Confirm Change',
      requiresOtp: requiresOtp,
      billRefNo: response.billRefNo,
    );
  }

  void _showSuccessScreenBottomSheet(ConfirmTransferResponse response) {
    final transaction = response.transaction;
    _bottomSheetsManager.showSuccessScreenBottomSheet(
        totalAmount: transaction.totalAmount,
        billAmount: transaction.billAmount,
        status: 'Paid',
        originalCurrency: transaction.originalCurrency,
        title: 'Your Change to birr was successfully exchanged.',
        {
          'Transaction Type': 'Change to Birr',
          'Amount In USD': '${transaction.billAmount} USD',
          'Exchange Rate': '1\$ = ${transaction.exchangeRate} ETB',
          'Amount In ETB': '${transaction.paidAmount} ETB',
          'Service Charge':
              '${transaction.serviceCharge} ${transaction.originalCurrency}',
          'VAT': '${transaction.vat} ${transaction.originalCurrency}',
          'Date': AppMapper.safeFormattedDate(transaction.createdAt),
          'Transaction Ref': transaction.billRefNo,
          'Wallet Ref': transaction.walletFTNumber,
          // "Bank Ref":  transaction.ftNumber
        });
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => sl<TransactionBloc>()),
      ],
      child: BlocConsumer<ExchangeRateBloc, ExchangeRateState>(
        listener: (context, state) {
          if (state is ExchangeRateLoaded) {
            final rate = state.rates.firstWhere(
              (rate) => rate.fromCurrency == 'USD' && rate.toCurrency == 'ETB',
              orElse: () => const ExchangeRateDTO(
                id: '0',
                fromCurrency: 'USD',
                toCurrency: 'ETB',
                rate: 0,
              ),
            );
            _currencyController.exchangeRate = rate.rate;
          }
        },
        builder: (context, state) {
          return BlocListener<ChangeToBirrBloc, ChangeToBirrState>(
            listener: (context, state) {
              if (state is TransferRulesChecked) {
                setState(() {
                  _isLoading = true;
                });

                context.read<ChangeToBirrBloc>().add(
                      ChangeToBirrRequested(_currencyController.numericAmount),
                    );
              } else if (state is ChangeToBirrFailure) {
                setState(() {
                  _isLoading = false;
                });
                CustomToastification(
                  context,
                  message: state.message,
                );
              } else if (state is ConfirmChangeToBirrState) {
                setState(() {
                  _isLoading = false;

                  _isCheckingPin = false;
                  _pinController.clear();
                });

                _showConfirmScreenBottomSheet(
                  state.response,
                  state.requiresOtp,
                );
              }
            },
            child: _buildScaffold(context),
          );
        },
      ),
    );
  }

  Widget _buildScaffold(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.dark.copyWith(
        statusBarColor: Colors.transparent,
      ),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Change to Birr'),
        ),
        body: SafeArea(
          bottom: true,
          child: BlocListener<TransactionBloc, TransactionState>(
            listenWhen: (previous, current) =>
                current is ConfirmTransferSuccess ||
                current is ConfirmTransferError,
            listener: (context, state) {
              if (state is ConfirmTransferSuccess) {
              } else if (state is ConfirmTransferError) {
                CustomToastification(
                  context,
                  message: state.message,
                );
              }
            },
            child: BlocConsumer<UserBloc, UserState>(
              listener: (context, state) {
                if (state is UserLoaded) {
                  setState(() {
                    ignoreWallletCheck = true;
                    _currencyController = CurrencyInputController(
                      currencyType: CurrencyType.usd,
                      maxBalance: state.user.wallets
                          .firstWhere(
                            (w) => w.currency == 'USD',
                          )
                          .balance,
                      ignoreWalletAmountCheck: ignoreWallletCheck,
                    );
                  });
                }
              },
              builder: (context, state) {
                if (state is UserLoading) return const CustomConnectLoader();
                if (state is UserLoaded) {
                  return CurrencyInputWidget(
                    controller: _currencyController,
                    transactionType: 'change_to_birr',
                    title: 'Change to Birr',
                    subtitle: 'Enter the amount you wish to convert to ETB.',
                    balanceLabel: CurrencyFormatter.getReadableAmount(
                      amount: state.user.wallets
                          .firstWhere(
                            (w) => w.currency == 'USD',
                          )
                          .balance,
                      currency: 'USD',
                    ),
                    isLoading: _isLoading,
                    showExchangeAmount:
                        _currencyController.exchangeRate != null,
                    exchangeAmount: _currencyController.exchangeRate,
                    onContinue: _onContinuePressed,
                  );
                } else
                  return const SizedBox.shrink();
              },
            ),
          ),
        ),
      ),
    );
  }
}
