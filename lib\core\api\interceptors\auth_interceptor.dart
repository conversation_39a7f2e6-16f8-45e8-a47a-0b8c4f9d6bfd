import 'package:cbrs/core/api/constants/api_constants.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/navigation/navigation_service.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

class AuthInterceptor extends Interceptor {
  AuthInterceptor(this._localDataSource, this._dio);
  final AuthLocalDataSource _localDataSource;
  final Dio _dio;
  bool _isRefreshing = false;

  @override
  Future<void> onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    debugPrint('data requested ${options.data}');
    debugPrint('url  requested ffffor ${options.uri}');

    if (!_shouldAddToken(options.path)) {
      return handler.next(options);
    }

    try {
      final token = await _localDataSource.getAuthToken();

      if (kDebugMode) {
        debugPrint('AUTH Token: $token');
      }

      if (token != null) {
        options.headers[ApiConstants.authorization] =
            '${ApiConstants.bearer} $token';
      }

      _addDefaultHeaders(options);
      handler.next(options);
    } catch (e) {
      debugPrint('On request erro catched');
      handler.reject(
        DioException(
          requestOptions: options,
          error: 'Failed to get auth token',
        ),
      );
    }
  }

  @override
  Future<void> onError(
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
    debugPrint('On erorr from the server ${err.message} and ob $err');

    debugPrint('\nOn erorr from the server ${err.response} and ob $err');

    final isSessionExpired = err.response?.statusCode == 401 ||
        err.response?.statusCode == 403 ||
        (err.response?.data is Map &&
            (err.response?.data['message'] as String?)
                    ?.toLowerCase()
                    .contains('session expired') ==
                true);

    if (isSessionExpired) {
      if (!_isRefreshing) {
        await _handleAuthError();
        // Force navigation to token login
        await sl<NavigationService>()
            .handleRedirect(AppRouteName.tokenDeviceLogin);
      }
    }
    handler.next(err);
  }

  Future<String?> _refreshToken() async {
    _isRefreshing = true;
    try {
      final response = await _dio.post(
        ApiEndpoints.refreshToken,
        options: Options(
          headers: {
            ApiConstants.contentType: ApiConstants.applicationJson,
            ApiConstants.accept: ApiConstants.applicationJson,
          },
        ),
      );

      if (response.data == null || response.data is! Map) {
        throw const ApiException(
          message: 'Invalid response format during token refresh',
          statusCode: 500,
        );
      }

      final dynamic tokenData = response.data['token'];
      if (tokenData == null || tokenData is! String) {
        throw const ApiException(
          message: 'Invalid token format in response',
          statusCode: 500,
        );
      }

      await _localDataSource.saveAuthToken(tokenData);
      return tokenData;
    } catch (e) {
      rethrow;
    } finally {
      _isRefreshing = false;
    }
  }

  void _retryRequest(
    DioException err,
    ErrorInterceptorHandler handler,
    String newToken,
  ) {
    final options = err.requestOptions;
    options.headers[ApiConstants.authorization] =
        '${ApiConstants.bearer} $newToken';

    _dio.fetch(options).then(
          (response) => handler.resolve(response),
          onError: (e) => handler.next(e as DioException),
        );
  }

  Future<void> _handleAuthError() async {
    try {
      await _localDataSource.clearAuthToken();
    } catch (e) {}
  }

  void _addDefaultHeaders(RequestOptions options) {
    options.headers.addAll({
      ApiConstants.contentType: ApiConstants.applicationJson,
      ApiConstants.accept: ApiConstants.applicationJson,
      ApiConstants.apiKey: ApiConstants.apiKeyValue,
    });
  }

  bool _shouldAddToken(String path) {
    final publicPaths = [
      ApiEndpoints.login,
      ApiEndpoints.register,
      ApiEndpoints.verifyEmail,
      ApiEndpoints.deviceCheck,
    ];
    return !publicPaths.contains(path);
  }
}
