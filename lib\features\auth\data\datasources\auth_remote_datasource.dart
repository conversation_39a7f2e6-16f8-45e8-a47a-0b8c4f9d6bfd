import 'package:cbrs/core/api/api_service.dart';
import 'package:cbrs/core/api/constants/api_endpoints.dart';
import 'package:cbrs/core/api/result.dart';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/core/services/device/device_service.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/auth/data/models/api_response_model.dart';
import 'package:cbrs/features/auth/data/models/sign_up_response.dart';
import 'package:cbrs/features/auth/data/models/user_dto.dart';
import 'package:flutter/foundation.dart';

abstract class AuthRemoteDataSource {
  Future<ApiResponseModel> signInWithEmail(String email);
  Future<ApiResponseModel> signInWithPhone(String phoneNumber);
  Future<ApiResponseModel> verifyOtp(
    String phoneNumber,
    String otp,
    String source,
  );
  Future<ApiResponseModel> verifyEmail(String email, String otp, String source);
  Future<SignUpResponse> signUp({
    required String fullName,
    required String email,
    required String phoneNumber,
    required String city,
    required String country,
    required String dateOfBirth,
    required String gender,
  });
  Future<ApiResponseModel> createPin(String pin, String source);
  Future<LocalUserDTO> loginWithPin(String pin);
  Future<ApiResponseModel> forgotPin(String email);
  Future<ApiResponseModel> forgotPinWithPhone(String phoneNumber);
  Future<ApiResponseModel> resetPin(String newPin, String confirmPin);
  Future<ApiResponseModel> unlinkDevice({String? email, String? phoneNumber});
  Future<void> logout();
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  AuthRemoteDataSourceImpl({
    required ApiService apiService,
    required DeviceService deviceService,
    required AuthLocalDataSource localDataSource,
  })  : _apiService = apiService,
        _deviceService = deviceService,
        _localDataSource = localDataSource;

  final ApiService _apiService;
  final DeviceService _deviceService;
  final AuthLocalDataSource _localDataSource;

  Future<ApiResponseModel> _handleApiResponse(
    Future<Result<Map<String, dynamic>>> resultFuture,
  ) async {
    try {
      final result = await resultFuture;

      return result.fold(
        ApiResponseModel.fromJson,
        (error) => ApiResponseModel(
          success: false,
          message: error.message,
        ),
      );
    } catch (e, stackTrace) {
      debugPrint('error $e');
      debugPrint('stacktrace $stackTrace');

      return const ApiResponseModel(
        success: false,
        message: 'An unexpected error occurredgg',
      );
    }
  }

  @override
  Future<ApiResponseModel> signInWithEmail(String email) async {
    return _handleApiResponse(
      _apiService.post(
        ApiEndpoints.forgetPin,
        data: {'email': email},
        requiresAuth: false,
        parser: (data) => data as Map<String, dynamic>,
      ),
    );
  }

  @override
  Future<ApiResponseModel> signInWithPhone(String phoneNumber) async {
    return _handleApiResponse(
      _apiService.post(
        ApiEndpoints.forgetPin,
        data: {'phoneNumber': phoneNumber},
        requiresAuth: false,
        parser: (data) => data as Map<String, dynamic>,
      ),
    );
  }

  @override
  Future<ApiResponseModel> verifyOtp(
    String phoneNumber,
    String otp,
    String source,
  ) async {
    final data = {
      'phoneNumber': phoneNumber,
      'OTPCode': int.parse(otp),
    };

    final result = await _apiService.post(
      source == 'device_login' || source == 'forgot_password'
          ? ApiEndpoints.verifyPin
          : ApiEndpoints.verifyEmail,
      data: source == 'device_login' || source == 'forgot_password'
          ? data
          : {'OTPCode': int.parse(otp)},
      requiresAuth: source != 'device_login' && source != 'forgot_password',
      parser: (data) => data as Map<String, dynamic>,
    );

    return result.fold(
      (data) async {
        if (data['token'] != null &&
            (source == 'device_login' || source == 'forgot_password')) {
          final token = data['token'] as String;
          await _localDataSource.saveAuthToken(token);
        }
        return ApiResponseModel.fromJson(data);
      },
      (error) => ApiResponseModel(
        success: false,
        message: error.message,
      ),
    );
  }

  @override
  Future<ApiResponseModel> verifyEmail(
    String email,
    String otp,
    String source,
  ) async {
    final data = {
      'email': email,
      'OTPCode': int.parse(otp),
    };

    final result = await _apiService.post(
      source == 'device_login' || source == 'forgot_password'
          ? ApiEndpoints.verifyPin
          : ApiEndpoints.verifyEmail,
      data: source == 'device_login' || source == 'forgot_password'
          ? data
          : {'OTPCode': int.parse(otp)},
      requiresAuth: source != 'device_login' && source != 'forgot_password',
      parser: (data) => data as Map<String, dynamic>,
    );

    return result.fold(
      (data) async {
        if (data['token'] != null &&
            (source == 'device_login' || source == 'forgot_password')) {
          final token = data['token'] as String;
          await _localDataSource.saveAuthToken(token);
        }
        return ApiResponseModel.fromJson(data);
      },
      (error) => ApiResponseModel(
        success: false,
        message: error.message,
      ),
    );
  }

  @override
  Future<SignUpResponse> signUp({
    required String fullName,
    required String email,
    required String phoneNumber,
    required String city,
    required String country,
    required String dateOfBirth,
    required String gender,
  }) async {
    final nameParts = fullName.trim().split(' ');
    final firstName = nameParts[0];
    final lastName = nameParts.length > 2
        ? nameParts.last
        : (nameParts.length == 2 ? nameParts[1] : '');

    debugPrint('Signup started');

    final data = {
      'firstName': firstName,
      'lastName': lastName,
      'address': '$city, $country',
      'city': city.trim(),
      'country': country.trim(),
      'realm': 'member',
      'dateOfBirth': dateOfBirth,
      'gender': gender,
      if (email.isNotEmpty) 'email': email.trim(),
      if (phoneNumber.isNotEmpty) 'phoneNumber': phoneNumber.trim(),
    };

    final result = await _apiService.post(
      ApiEndpoints.register,
      data: data,
      requiresAuth: false,
      parser: (data) => data as Map<String, dynamic>,
    );

    return result.fold(SignUpResponse.fromJson, (error) {
      debugPrint("Erro from signup response ${error}");
      debugPrint("Erro from signup response messae ${error.message}");

      return SignUpResponse(
        message: error.message,
        success: false,
        token: '',
        otpCode: 0,
      );
    });
  }

  @override
  Future<ApiResponseModel> createPin(String pin, String source) async {
    final deviceUuid = await _deviceService.getDeviceId();

    final data = {
      'device_uuid': deviceUuid,
      'login_pin': pin,
    };

    return _handleApiResponse(
      _apiService.post(
        ApiEndpoints.createPin,
        data: data,
        parser: (data) => data as Map<String, dynamic>,
      ),
    );
  }

  @override
  Future<LocalUserDTO> loginWithPin(String pin) async {
    final deviceUuid = await _deviceService.getDeviceId();

    final data = {
      'device_uuid': deviceUuid,
      'login_pin': pin,
    };

    final result = await _apiService.post(
      ApiEndpoints.login,
      data: data,
      requiresAuth: false,
      parser: (data) => data as Map<String, dynamic>,
    );

    return result.fold(
      (data) async {
        if (data['success'] == false) {
          throw ApiException(
            message: data['message'] as String? ?? 'Login failed',
            statusCode: 401,
          );
        }

        final userData = data['data'] as Map<String, dynamic>;

        debugPrint("ghsdbhds ${userData['memberLevel']}");

        final token = data['accessToken'] as String?;

        if (token == null) {
          throw const ApiException(
            message: 'No access token in response',
            statusCode: 500,
          );
        }

        final completeUserData = {
          ...userData,
          'token': token,
        };

        await _localDataSource.clearAuthToken();
        await _localDataSource.clearUserData();
        await _localDataSource.saveAuthToken(token);

        final userDTO = LocalUserDTO.fromJson(completeUserData);

        debugPrint('MmMmmmeber level ${userDTO.memberLevel} ');
        await _localDataSource.saveUserData(userDTO);

        return userDTO;
      },
      (error) => throw ApiException(
        message: error.message,
        statusCode: error.statusCode ?? 401,
      ),
    );
  }

  @override
  Future<ApiResponseModel> forgotPin(String email) async {
    return _handleApiResponse(
      _apiService.post(
        ApiEndpoints.forgetPin,
        data: {'email': email},
        requiresAuth: false,
        parser: (data) => data as Map<String, dynamic>,
      ),
    );
  }

  @override
  Future<ApiResponseModel> forgotPinWithPhone(String phoneNumber) async {
    return _handleApiResponse(
      _apiService.post(
        ApiEndpoints.forgetPin,
        data: {'phoneNumber': phoneNumber},
        requiresAuth: false,
        parser: (data) => data as Map<String, dynamic>,
      ),
    );
  }

  @override
  Future<ApiResponseModel> resetPin(String newPin, String confirmPin) async {
    return _handleApiResponse(
      _apiService.post(
        ApiEndpoints.forgetPin,
        data: {
          'newPin': newPin,
          'confirmPin': confirmPin,
        },
        parser: (data) => data as Map<String, dynamic>,
      ),
    );
  }

  @override
  Future<ApiResponseModel> unlinkDevice({
    String? email,
    String? phoneNumber,
  }) async {
    final deviceUuid = await _deviceService.getDeviceId();
    final data = <String, String>{
      'device_uuid': deviceUuid,
    };
    if (email != null) data['email'] = email;
    if (phoneNumber != null) data['phoneNumber'] = phoneNumber;

    final result = await _apiService.post(
      ApiEndpoints.unlinkDevice,
      data: data,
      parser: (data) => data as Map<String, dynamic>,
    );

    return result.fold(
      ApiResponseModel.fromJson,
      (error) => ApiResponseModel(
        success: false,
        message: error.message,
      ),
    );
  }

  @override
  Future<void> logout() async {
    try {
      await _localDataSource.clearAuthToken();
      await _localDataSource.clearUserData();
    } catch (e) {
      // Silently handle logout errors
    }
  }
}
