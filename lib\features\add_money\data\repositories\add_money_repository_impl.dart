import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/add_money/data/datasources/add_money_remote_datasource.dart';
import 'package:cbrs/features/add_money/data/models/add_money_response_model.dart';
import 'package:cbrs/features/add_money/domain/entities/linked_account.dart';
import 'package:cbrs/features/add_money/domain/repositories/add_money_repository.dart';
import 'package:cbrs/features/send_money/data/models/check_transfer_rules_response.dart';
import 'package:cbrs/features/send_money/data/models/otp_resend_response.dart';
import 'package:dartz/dartz.dart';

class AddMoneyRepositoryImpl implements AddMoneyRepository {
  final AddMoneyRemoteDataSource _remoteDataSource;

  const AddMoneyRepositoryImpl(this._remoteDataSource);

  @override
  ResultFuture<PaginatedLinkedAccounts> getLinkedAccounts({
    required int page,
    required int limit,
    String status = 'LINKED',
  }) async {
    try {
      final result = await _remoteDataSource.getLinkedAccounts(
        page: page,
        limit: limit,
        status: status,
      );
      return Right(result);
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message, errors: e.errors));
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<LinkedAccount> checkAccountBalance({
    required String bankId,
    required String accountNumber,
  }) async {
    try {
      final result = await _remoteDataSource.checkAccountBalance(
        bankId: bankId,
        accountNumber: accountNumber,
      );
      return Right(result);
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message, errors: e.errors));
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<CheckTransferRulesResponse> checkTransferRules({
    required double amount,
    required String currency,
    required String bankId,
    required String accountNumber,

  }) async {
    try {
      final result = await _remoteDataSource.checkTransferRules(
        amount: amount,
        currency: currency,
        accountNumber: accountNumber,
        bankId: bankId
      );
      return Right(result);
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message, errors: e.errors));
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<AddMoneyResponseModel> addMoney({
    required String accountNumber,
    required String bankId,
    required double amount,
    required String currency,
    required String senderName,
  }) async {
    try {
      final result = await _remoteDataSource.addMoney(
        accountNumber: accountNumber,
        bankId: bankId,
        amount: amount,
        currency: currency,
        senderName: senderName,
      );
      return Right(result);
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message, errors: e.errors));
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<AddMoneyResponseModel> submitPin({
    required String pin,
    required String billRefNo,
    String? otp,
  }) async {
    try {
      final result = await _remoteDataSource.submitPin(
        pin: pin,
        billRefNo: billRefNo,
        otp: otp,
      );
      return Right(result);
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message, errors: e.errors));
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<OtpResendResponse> resendOtp({
    required String billRefNo,
    required String otpFor,
  }) async {
    try {
      final result = await _remoteDataSource.resendOtp(
        billRefNo: billRefNo,
        otpFor: otpFor,
      );
      return Right(result);
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message, errors: e.errors));
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }

  @override
  ResultFuture<AddMoneyResponseModel> verifyOtp({
    required String billRefNo,
    required String otpFor,
    required int otpCode,
  }) async {
    try {
      final result = await _remoteDataSource.verifyOtp(
        billRefNo: billRefNo,
        otpFor: otpFor,
        otpCode: otpCode,
      );
      return Right(result);
    } on ValidationException catch (e) {
      return Left(ValidationFailure(message: e.message, errors: e.errors));
    } on ApiException catch (e) {
      return Left(ServerFailure(message: e.message));
    }
  }
}
