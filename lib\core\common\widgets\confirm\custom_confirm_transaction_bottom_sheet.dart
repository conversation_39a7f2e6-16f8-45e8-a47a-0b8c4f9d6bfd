import 'package:cbrs/core/common/models/transaction_display_model.dart';
import 'package:cbrs/core/common/models/transaction_types.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomConfirmTransactionBottomSheet extends StatelessWidget {
  /// Creates a customizable transaction confirmation bottom sheet
  ///
  /// [data] contains the transaction details
  /// [transactionType] defines the type of transaction using the string value from TransactionType enum
  /// [onContinue] is called when the user confirms the transaction
  /// [confirmButtonText] allows customizing the confirm button text
  const CustomConfirmTransactionBottomSheet(
      {required this.onContinue,
      required this.data,
      required this.originalCurrency,
      this.transactionType = 'bank_transfer',
      this.confirmButtonText = 'Confirm',
      this.isLoading = false,
      this.status = 'Wait',
      required this.totalAmount,
      this.isFromMoneyRequest = false,
      super.key,
      this.reasonRequestController,
      this.isBtnActive = true,
      required this.billAmount});

  final VoidCallback onContinue;
  final Map<String, dynamic> data;
  final String transactionType;
  final String confirmButtonText;
  final String status;
  final String originalCurrency;
  final TextEditingController? reasonRequestController;

  final double totalAmount;
  final double billAmount;

  final bool isLoading;
  final bool isBtnActive;
  final bool isFromMoneyRequest;

  @override
  Widget build(BuildContext context) {
    // Create display model based on transaction type

    return IntrinsicHeight(
      child: Container(
        clipBehavior: Clip.antiAlias,
        decoration: const ShapeDecoration(
          color: Color(0xFFFCFCFC),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(36),
              topRight: Radius.circular(36),
            ),
          ),
        ),
        child: Column(
          children: [
            // Handle bar
            Center(
              child: Container(
                width: 64,
                height: 6,
                margin: EdgeInsets.only(
                  top: 16.h,
                  left: 16.w,
                  right: 16.w,
                  bottom: 8,
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFFBCBCBC),
                  borderRadius: BorderRadius.circular(40),
                ),
              ),
            ),
            // Main content
            Flexible(
              child: SingleChildScrollView(
                physics: const ClampingScrollPhysics(),
                child: Container(
                  color: Colors.white,
                  padding: EdgeInsets.only(
                    left: 16.w,
                    right: 16.w,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(height: 8),
                      // Amount and title section
                      _buildHeaderSection(),
                      SizedBox(height: 12.h),
                      // Transaction details section
                      _buildDetailsSection(),
                      SizedBox(height: 38.h),

                      SizedBox(height: 38.h),
                    ],
                  ),
                ),
              ),
            ),
            // Bottom button section
            _buildBottomButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderSection() {
    final billAmt = AppMapper.safeFormattedNumberWithDecimal(billAmount);

    return Container(
      padding: EdgeInsets.only(top: 11.h, bottom: 10.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(32),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            blurRadius: 100,
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Column(
                children: [
                  Image.asset(
                    MediaRes.roundedBirrTransactionIcon,
                    height: 100.h,
                  ),
                  CustomBuildText(
                    text: '$billAmt $originalCurrency',
                    textAlign: TextAlign.center,
                    fontSize: 32.sp,
                    fontWeight: FontWeight.w700,
                  ),
                  SizedBox(height: 8.h),
                  CustomBuildText(
                    text: 'Confirm transfer',
                    textAlign: TextAlign.center,
                    fontSize: 14.sp,
                    caseType: 'default',
                    color: const Color(0xFF6D6D6D),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailsSection() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 10.w,
        vertical: 16.h,
      ),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F8F8),
        borderRadius: BorderRadius.circular(24),
      ),
      child: Column(
        children: [
          Row(
            children: [
              CustomBuildText(
                text: 'Transaction Detail',
                textAlign: TextAlign.center,
                fontSize: 14.sp,
                fontWeight: FontWeight.w700,
                caseType: 'default',
              ),
            ],
          ),
          SizedBox(height: 10.h),
          // Transaction details container
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(24),
            ),
            child: Column(
              children: [
                // Status row if status is available
                Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 5,
                    horizontal: 4,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomBuildText(
                        text: 'Status',
                        textAlign: TextAlign.center,
                        fontSize: 14.sp,
                        caseType: 'default',
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 14.w,
                          vertical: 6.h,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFFFFFAE0),
                          borderRadius: BorderRadius.circular(32),
                        ),
                        child: CustomBuildText(
                          text: status,
                          textAlign: TextAlign.center,
                          fontSize: 13.sp,
                          caseType: 'default',
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFFFF9000),
                        ),
                      ),
                    ],
                  ),
                ),

                ...data.entries.map(
                  (
                    entry,
                  ) =>
                      _buildTransactionList(
                    label: entry.key,
                    value: entry.value.toString(),
                  ),
                ),
                // All transaction details
                // ...model.details.map(
                //   (detail) => _buildTransactionList(
                //     label: detail.label,
                //     value: detail.value,
                //   ),
                // ),
              ],
            ),
          ),
          if (isFromMoneyRequest)
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(24),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomBuildText(
                    text: 'Request Reason (Optional)',
                    textAlign: TextAlign.left,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    caseType: 'default',
                  ),
                  SizedBox(height: 8.h),
                  TextField(
                    controller: reasonRequestController,
                    decoration: InputDecoration(
                      hintText: 'Enter reason for money request',
                      hintStyle: TextStyle(
                        color: const Color(0xFFAAAAAA),
                        fontSize: 14.sp,
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: Colors.grey.shade300,
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: Colors.grey.shade300,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: Colors.grey.shade400,
                        ),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 12,
                      ),
                    ),
                    maxLines: 3,
                    minLines: 1,
                    textCapitalization: TextCapitalization.sentences,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),

          SizedBox(height: 14.h),
          // Total amount summary
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(14),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CustomBuildText(
                  text: 'Total Amount',
                  textAlign: TextAlign.center,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w700,
                  caseType: 'default',
                ),
                CustomBuildText(
                  text:
                      '${AppMapper.safeFormattedNumberWithDecimal(totalAmount)} $originalCurrency',
                  textAlign: TextAlign.center,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w700,
                  caseType: 'default',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButton() {
    return Container(
      padding: EdgeInsets.only(
        top: 18.h,
        left: 16.w,
        right: 16.w,
        bottom: 20.h,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            blurRadius: 8,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: CustomRoundedBtn(
        btnText: confirmButtonText,
        isLoading: isLoading,
        onTap: onContinue,
        isBtnActive: isBtnActive,
      ),
    );
  }

  Widget _buildTransactionList({
    required String label,
    required String value,
  }) {
    return value.isEmpty
        ? const SizedBox.shrink()
        : Container(
            padding: const EdgeInsets.only(bottom: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomBuildText(
                  text: label,
                  textAlign: TextAlign.center,
                  fontSize: 14.sp,
                  caseType: 'default',
                ),
                const SizedBox(
                  width: 20,
                ),
                Flexible(
                  child: CustomBuildText(
                    text: value,
                    textAlign: TextAlign.right,
                    fontSize: 14.sp,
                    caseType: 'default',
                  ),
                ),
              ],
            ),
          );
  }
}
