import 'package:cbrs/core/common/global_variable.dart';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/constants/storage_keys.dart';
import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:cbrs/core/services/connectivity/connectivity_controller.dart';
import 'package:cbrs/core/services/device/device_controller.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/core/services/storage/hive_box_manager.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gif/gif.dart';
import 'package:go_router/go_router.dart';

class CustomConnectLoader extends StatefulWidget {
  const CustomConnectLoader({super.key});

  @override
  State<CustomConnectLoader> createState() => _CustomConnectLoaderState();
}

class _CustomConnectLoaderState extends State<CustomConnectLoader>
    with TickerProviderStateMixin {
  final bool _isCheckingConnectivity = false;

  late HiveBoxManager _hiveBoxManager;
  late AuthLocalDataSource _authLocalDataSource;
  late GifController _controller;

  @override
  void initState() {
    _hiveBoxManager = sl<HiveBoxManager>();
    _authLocalDataSource = sl<AuthLocalDataSource>();
    _controller = GifController(vsync: this);
    loaderGif = const AssetImage('assets/animations/loading_animation.gif');

    super.initState();
  }

  late AssetImage loaderGif;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    precacheImage(loaderGif, context);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Gif(
        image: loaderGif,
        controller: _controller,
        autostart: Autostart.loop,
        width: 124,
        height: 124,
        placeholder: (context) => Container(
          padding: const EdgeInsets.only(top: 94, left: 16),
          child: const CustomBuildText(
            text: 'Connect',
            caseType: 'all',
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF054D41),
          ),
        ),
      ),
    );
  }
}
