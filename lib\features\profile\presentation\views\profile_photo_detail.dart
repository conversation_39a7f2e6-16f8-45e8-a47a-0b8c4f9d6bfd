import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

class ProfilePhotoDetail extends StatelessWidget {
  final String photoUrl;

  const ProfilePhotoDetail({
    super.key,
    required this.photoUrl,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0,
      ),
      body: Center(
        child: InteractiveViewer(
          panEnabled: true,
          minScale: 1.0,
          maxScale: 4.0,
          child: Hero(
            tag: photoUrl,
            child: CachedNetworkImage(
              imageUrl: photoUrl,
              placeholder: (context, url) => const CircularProgressIndicator(),
              errorWidget: (context, url, error) => const Icon(
                Icons.error,
                color: Colors.red,
                size: 48,
              ),
              fit: BoxFit.contain,
            ),
          ),
        ),
      ),
    );
  }
}
