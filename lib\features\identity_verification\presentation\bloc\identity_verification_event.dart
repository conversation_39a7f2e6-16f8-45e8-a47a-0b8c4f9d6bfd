import 'dart:io';
import 'package:equatable/equatable.dart';

abstract class IdentityVerificationEvent extends Equatable {
  const IdentityVerificationEvent();

  @override
  List<Object?> get props => [];
}

class UploadDocumentEvent extends IdentityVerificationEvent {
  const UploadDocumentEvent({
    required this.frontPhoto,
    required this.backPhoto,
    required this.selfiePhoto,
    this.documentType = 'id_card',
  });

  final File frontPhoto;
  final File backPhoto;
  final File selfiePhoto;
  final String documentType;

  @override
  List<Object?> get props => [frontPhoto, backPhoto, selfiePhoto, documentType];
}

class ResetIdentityVerificationEvent extends IdentityVerificationEvent {
  const ResetIdentityVerificationEvent();
}
