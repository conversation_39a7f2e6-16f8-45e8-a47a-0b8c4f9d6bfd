import 'package:get/get.dart';


extension StringExtension on String {
  /// Capitalise like: Hello world
  String toCapitalized() =>
      length > 0 ? '${this[0].toUpperCase()}${substring(1)}' : '';
  /// Capitalise like: Hello world from dart code

  String get inCaps => '${this[0].toUpperCase()}${this.substring(1)}';
  /// Capitalise like: HELLO WORLD FROM DART CODES

  String get allInCaps => this.toUpperCase();
/// Hello World From Dart Codes
  String get capitalizeFirstofEach =>
      this.split(" ").map((str) => str.capitalize).join(" ");
}
