import 'package:bloc/bloc.dart';
import 'package:cbrs/core/common/global_variable.dart';
import 'package:cbrs/core/services/device/device_controller.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/utils/get_app_theme_controller.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/auth/data/models/device_check_response.dart';
import 'package:cbrs/features/auth/data/models/user_dto.dart';
import 'package:cbrs/features/auth/domain/entities/response_handler.dart';
import 'package:cbrs/features/auth/domain/entities/user.dart' show LocalUser;
import 'package:cbrs/features/auth/domain/usecases/create_pin.dart';
import 'package:cbrs/features/auth/domain/usecases/forgot_pin.dart';
import 'package:cbrs/features/auth/domain/usecases/forgot_pin_with_phone.dart';
import 'package:cbrs/features/auth/domain/usecases/login_with_pin.dart';
import 'package:cbrs/features/auth/domain/usecases/logout.dart';
import 'package:cbrs/features/auth/domain/usecases/params/auth_params.dart';
import 'package:cbrs/features/auth/domain/usecases/resend_email_verification.dart';
import 'package:cbrs/features/auth/domain/usecases/resend_otp.dart';
import 'package:cbrs/features/auth/domain/usecases/reset_pin.dart';
import 'package:cbrs/features/auth/domain/usecases/sign_in_with_email.dart';
import 'package:cbrs/features/auth/domain/usecases/sign_in_with_phone.dart';
import 'package:cbrs/features/auth/domain/usecases/sign_up.dart';
import 'package:cbrs/features/auth/domain/usecases/unlink_device.dart';
import 'package:cbrs/features/auth/domain/usecases/verify_email.dart';
import 'package:cbrs/features/auth/domain/usecases/verify_otp.dart';
import 'package:cbrs/features/send_money/domain/entities/recent_transaction_hive.dart';
import 'package:cbrs/features/transfer_to_wallet/domain/entities/recent_wallet_transfer_hive.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';

part 'auth_event.dart';
part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  AuthBloc({
    required SignInWithEmail signInWithEmail,
    required SignInWithPhone signInWithPhone,
    required VerifyOtp verifyOtp,
    required VerifyEmail verifyEmail,
    required ResendOtp resendOtp,
    required ResendEmailVerification resendEmailVerification,
    required SignUp signUp,
    required CreatePin createPin,
    required LoginWithPin loginWithPin,
    required ForgotPin forgotPin,
    required ForgotPinWithPhone forgotPinWithPhone,
    required ResetPin resetPin,
    required UnlinkDevice unlinkDevice,
    required Logout logout,
    required AuthLocalDataSource localDataSource,
  })  : _localDataSource = localDataSource,
        _signInWithEmail = signInWithEmail,
        _signInWithPhone = signInWithPhone,
        _verifyOtp = verifyOtp,
        _verifyEmail = verifyEmail,
        _resendOtp = resendOtp,
        _resendEmailVerification = resendEmailVerification,
        _signUp = signUp,
        _createPin = createPin,
        _loginWithPin = loginWithPin,
        _forgotPin = forgotPin,
        _forgotPinWithPhone = forgotPinWithPhone,
        _resetPin = resetPin,
        _unlinkDevice = unlinkDevice,
        super(const AuthInitial()) {
    on<SignInWithEmailEvent>(_handleSignInWithEmail);
    on<SignInWithPhoneEvent>(_handleSignInWithPhone);
    on<VerifyOtpEvent>(_handleVerifyOtp);
    on<VerifyEmailEvent>(_handleVerifyEmail);
    on<ResendOtpEvent>(_handleResendOtp);
    on<ResendEmailVerificationEvent>(_handleResendEmailVerification);
    on<SignUpEvent>(_handleSignUp);
    on<CreatePinEvent>(_handleCreatePin);
    on<LoginWithPinEvent>(_handleLoginWithPin);
    on<ForgotPinEvent>(_handleForgotPin);
    on<ForgotPinWithPhoneEvent>(_handleForgotPinWithPhone);
    on<ResetPinEvent>(_handleResetPin);
    on<UnlinkDeviceEvent>(_handleUnlinkDevice);
    on<LogoutEvent>(_handleLogout);
    on<CheckBiometricEvent>(_handleCheckBiometric);
    on<InitializeAuthEvent>(_handleInitializeAuth);
  }
  LocalUser? _authenticatedUser;
  final AuthLocalDataSource _localDataSource;

  LocalUser? get authenticatedUser => _authenticatedUser;
  bool get isAuthenticated {
    debugPrint('\n=== CHECKING AUTHENTICATION STATUS ===');
    final hasUser = _authenticatedUser != null;
    final hasToken = _authenticatedUser?.token != null;
    final isValid = hasUser && hasToken;

    if (!isValid) {
      // Try to restore from cache synchronously
      _initializeAuthStateSync();
    }

    return _authenticatedUser != null && _authenticatedUser?.token != null;
  }

  void _initializeAuthStateSync() {
    try {
      final userData = _localDataSource.getCachedUserData();
      userData.then((user) {
        if (user != null) {
          _authenticatedUser = user;
        }
      });
    } catch (e) {
      debugPrint('Error initializing auth state: $e');
    }
  }

  final SignInWithEmail _signInWithEmail;
  final SignInWithPhone _signInWithPhone;
  final VerifyOtp _verifyOtp;
  final VerifyEmail _verifyEmail;
  final ResendOtp _resendOtp;
  final ResendEmailVerification _resendEmailVerification;
  final SignUp _signUp;
  final CreatePin _createPin;
  final LoginWithPin _loginWithPin;
  final ForgotPin _forgotPin;
  final ForgotPinWithPhone _forgotPinWithPhone;
  final ResetPin _resetPin;
  final UnlinkDevice _unlinkDevice;

  Future<void> _handleInitializeAuth(
    InitializeAuthEvent event,
    Emitter<AuthState> emit,
  ) async {
    debugPrint('\n=== INITIALIZING AUTH STATE ===');
    try {
      final token = await _localDataSource.getAuthToken();
      final userData = await _localDataSource.getCachedUserData();

      if (userData != null) {
        // Create user object from cached data
        _authenticatedUser = LocalUser(
          id: userData.id,
          firstName: userData.firstName,
          middleName: userData.middleName,
          lastName: userData.lastName,
          email: userData.email,
          phoneNumber: userData.phoneNumber,
          token: token, // This might be null
          city: userData.city,
          memberCode: userData.memberCode,
          realm: userData.realm,
          address: userData.address,
          enabled: userData.enabled,
          isDeleted: userData.isDeleted,
          isVerified: userData.isVerified,
          isEmailVerified: userData.isEmailVerified,
          isPhoneVerified: userData.isPhoneVerified,
          dateJoined: userData.dateJoined,
          lastModified: userData.lastModified,
          deviceUUID: userData.deviceUUID,
          country: userData.country,
          avatar: userData.avatar,
          dateOfBirth: userData.dateOfBirth,
          gender: userData.gender,
          memberLevel: userData.memberLevel
        );

        if (token != null) {
          emit(LoggedInWithPinState(_authenticatedUser!));
        } else {
          // Don't make API calls when token is missing
          emit(TokenExpiredState(_authenticatedUser!));
          return;
        }
      } else {
        _authenticatedUser = null;
        emit(const AuthInitial());
      }
    } catch (e) {
      debugPrint('Error initializing auth state: $e');
      _authenticatedUser = null;
      emit(AuthError(e.toString()));
    }
  }

  Future<void> _handleSignInWithEmail(
    SignInWithEmailEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());
    final result = await _signInWithEmail(EmailParams(email: event.email));
    result.fold(
      (failure) => emit(AuthError(failure.message)),
      (success) => emit(EmailVerificationSentState(success)),
    );
  }

  Future<void> _handleSignInWithPhone(
    SignInWithPhoneEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());
    final result = await _signInWithPhone(
      PhoneParams(phoneNumber: event.phoneNumber),
    );
    result.fold(
      (failure) => emit(AuthError(failure.message)),
      (success) => emit(OtpSentState(success)),
    );
  }

  Future<void> _handleVerifyOtp(
    VerifyOtpEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());
    try {
      final result = await _verifyOtp(
        OtpParams(
          phoneNumber: event.phoneNumber,
          otp: event.otp,
          source: event.source,
        ),
      );
      debugPrint('What do ypu do');
      result.fold(
        (failure) {
          debugPrint('Error emit $emit');
          emit(AuthError(failure.message));
        },
        (success) => emit(OtpVerifiedState(success)),
      );
    } catch (e) {
      debugPrint('Email verification error: $e');
      emit(AuthError(e.toString()));
    }
  }

  Future<void> _handleVerifyEmail(
    VerifyEmailEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());
    try {
      final result = await _verifyEmail(
        EmailVerificationParams(
          email: event.email,
          otp: event.otp,
          source: event.source,
        ),
      );

      result.fold(
        (failure) {
          debugPrint('Email verification failed: ${failure.message}');
          emit(AuthError(failure.message));
        },
        (success) {
          debugPrint('Email verification successful');
          emit(EmailVerifiedState(success));
        },
      );
    } catch (e) {
      debugPrint('Email verification error: $e');
      emit(AuthError(e.toString()));
      // emit(AuthError('Something happened'));
    }
  }

  Future<void> _handleSignUp(
    SignUpEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    final result = await _signUp(
      SignUpParams(
        fullName: event.fullName,
        email: event.email,
        phoneNumber: event.phoneNumber,
        city: event.city,
        country: event.country,
        dateOfBirth: event.dateOfBirth,
        gender: event.gender,
      ),
    );

    result.fold(
      (failure) => emit(AuthError(failure.message)),
      (response) {
        emit(
          SignedUpState(
            email: event.email,
            phoneNumber: event.phoneNumber,
            otp: response.otpCode,
            success: response.success,
            message: response.message,
          ),
        );
      },
    );
  }

  /// setps followed during create pin
  /// 1. new pin must be set
  /// 2. already cached data must be delete from the phone storage
  /// 3. tab bar set to 0
  /// 4. based on new pin login request would be sent to #BE to log user with essential data
  /// 5. redirect to home screen

  Future<void> _handleCreatePin(
    CreatePinEvent event,
    Emitter<AuthState> emit,
  ) async {
    if (emit.isDone) return;

    debugPrint('\n=== HANDLING CREATE PIN ===');
    emit(const AuthLoading());

    try {
      // Step 1: Create PIN
      final result = await _createPin(
        PinParams(pin: event.pin, source: event.source),
      );

      // if (emit.isDone) return;

      await result.fold(
        (failure) async {
          debugPrint('PIN creation failed: ${failure.message}');
          emit(AuthError(failure.message));
        },
        (success) async {
          try {
            // Step 2: Save PIN locally
            debugPrint('PIN saved locally g ${success.message}');
            emit(const PinCreatedState());
            emit(const AuthLoading());

            await Hive.deleteBoxFromDisk(
              'recentTransactionsBox',
            ); // after every pin set recent wallet and bank transfer should be reset.
            await Hive.openBox<RecentTransactionHive>('recentTransactionsBox');

            await Hive.deleteBoxFromDisk(
              'recentWalletTransactionsBox',
            ); // after every pin set recent wallet and bank transfer should be reset.
            await Hive.openBox<RecentWalletTransferHive>(
              'recentWalletTransactionsBox',
            );

            /// set global variable index = 0, to make sure app alwasy starts from tabbar => 0

            GlobalVariable.currentTabIndex = 0;

            // Step 3: Attempt login with new PIN
            final loginResult = await _loginWithPin(
              PinParams(pin: event.pin),
            );

            if (emit.isDone) return;

            await loginResult.fold(
              (failure) {
                debugPrint('Auto-login failed: ${failure.message}');
                emit(AuthError(failure.message));
              },
              (user) async {
                try {
                  final userDto = LocalUserDTO.fromLocalUser(user);
                  await _localDataSource.saveAuthToken(user.token ?? '');
                  await _localDataSource.cacheUserData(userDto);
                  _authenticatedUser = user;

                  if (!emit.isDone) {
                    emit(LoggedInWithPinState(user));
                    debugPrint('=== CREATE PIN AND LOGIN COMPLETED ===\n');
                  }
                } catch (e) {
                  debugPrint('Error caching user data: $e');
                  emit(AuthError('Failed to save user data: $e'));
                }
              },
            );
          } catch (e) {
            debugPrint('Error during PIN creation completion: $e');
            emit(AuthError(e.toString()));
          }
        },
      );
    } catch (e) {
      debugPrint('Unexpected error during PIN creation: $e');
      if (!emit.isDone) {
        emit(AuthError(_cleanErrorMessage(e.toString())));
      }
    }
  }

  Future<void> _handleLoginWithPin(
    LoginWithPinEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    try {
      GlobalVariable.currentTabIndex = 0;

      final result = await _loginWithPin(PinParams(pin: event.pin));

      await result.fold(
        (failure) async {
          debugPrint('Login failed: ${failure.message}');
          _authenticatedUser = null;
          emit(AuthError(failure.message));
        },
        (user) async {
          if (user.token == null) {
            emit(const AuthError('Authentication failed - no token received'));
            return;
          }

          _authenticatedUser = user;

          await Future.wait([
            _localDataSource.saveAuthToken(user.token!),
            _localDataSource.cacheUserData(LocalUserDTO.fromLocalUser(user)),
          ]);

          emit(LoggedInWithPinState(user));
        },
      );
    } catch (e) {
      emit(AuthError(e.toString()));
    }
  }

  Future<void> _handleForgotPin(
    ForgotPinEvent event,
    Emitter<AuthState> emit,
  ) async {
    if (event.email.trim().isEmpty) {
      emit(const AuthError('Email is required'));
      return;
    }

    emit(const AuthLoading());

    debugPrint('📧 Processing forgot PIN for email: ${event.email}');
    final result = await _forgotPin(EmailParams(email: event.email.trim()));

    result.fold(
      (failure) {
        emit(AuthError(failure.message));
      },
      (success) {
        emit(EmailVerificationSentState(success));
      },
    );
  }

  Future<void> _handleForgotPinWithPhone(
    ForgotPinWithPhoneEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());
    final result = await _forgotPinWithPhone(
      PhoneParams(phoneNumber: event.phoneNumber),
    );
    result.fold(
      (failure) => emit(AuthError(failure.message)),
      (success) => emit(PinResetRequestedState(success)),
    );
  }

  Future<void> _handleResetPin(
    ResetPinEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());
    final result = await _resetPin(
      ResetPinParams(
        newPin: event.newPin,
        confirmPin: event.confirmPin,
      ),
    );
    result.fold(
      (failure) => emit(AuthError(failure.message)),
      (_) => emit(const PinResetState()),
    );
  }

  Future<void> _handleResendOtp(
    ResendOtpEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());
    final result =
        await _resendOtp(PhoneParams(phoneNumber: event.phoneNumber));
    result.fold(
      (failure) => emit(AuthError(failure.message)),
      (success) => emit(OtpSentState(success)),
    );
  }

  Future<void> _handleResendEmailVerification(
    ResendEmailVerificationEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());
    final result =
        await _resendEmailVerification(EmailParams(email: event.email));
    result.fold(
      (failure) => emit(AuthError(failure.message)),
      (success) => emit(EmailVerificationSentState(success)),
    );
  }

  Future<void> _handleUnlinkDevice(
    UnlinkDeviceEvent event,
    Emitter<AuthState> emit,
  ) async {
    debugPrint('Unlinking device 🔗🔗🔗');

    if (emit.isDone) return;
    emit(const AuthLoading());

    try {
      final userData = await _localDataSource.getCachedUserData();

      final email = userData?.email;
      final phoneNumber = userData?.phoneNumber;

      // Check if both identifiers are empty or null
      if ((email?.isEmpty ?? true) && (phoneNumber?.isEmpty ?? true)) {
        emit(
          const AuthError(
            'No email or phone number found for device unlinking',
          ),
        );
        return;
      }

      final result = await _unlinkDevice(
        DeviceParams(
          email: email?.isNotEmpty ?? false ? email : null,
          phoneNumber: phoneNumber?.isNotEmpty ?? false ? phoneNumber : null,
        ),
      );

      if (emit.isDone) return;

      await result.fold(
        (failure) async {
          emit(AuthError(failure.message));
        },
        (success) async {
          // Clear auth data after successful unlink
          await _localDataSource.clearAuthToken();
          await _localDataSource.clearUserData();
          GlobalVariable.isDeviceRegisteredOnConnect = false;
          GlobalVariable.isDeviceVerified = false;

          emit(DeviceUnlinkedState(success));
        },
      );
    } catch (e) {
      debugPrint('Error during unlink device: $e');
      if (!emit.isDone) {
        emit(AuthError(e.toString()));
      }
    }
  }

  Future<void> _handleLogout(
    LogoutEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    try {
      // Before we clear the user profile we have to save user name temporarily on the memory
      final userData = await _localDataSource.getCachedUserData();
      await sl<DeviceCheckController>().reSetDeviceData(
        DeviceCheckData(
          email: userData?.email ?? '',
          phoneNumber: userData?.phoneNumber ?? '',
          firstName: userData?.firstName ?? '',
          middleName: userData?.middleName ?? '',
          lastName: userData?.lastName ?? '',
          avatar: userData?.avatar ?? '',
        ),
      );

      // Reset to USD wallet
      final themeController = Get.find<GetAppThemeController>();
      await themeController.forceSetTheme('USD');

      await Future.wait([
        _localDataSource.clearAuthToken(),
        _localDataSource.clearUserData(),
        _localDataSource.logout(),
      ]);

      _authenticatedUser = null;
      emit(const LoggedOutState());
    } catch (e) {
      debugPrint('Error during logout: $e');
      emit(AuthError(e.toString()));
    }
  }

  Future<void> _handleCheckBiometric(
    CheckBiometricEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());
    try {
      final isAuthenticated =
          await _localDataSource.authenticateWithBiometrics();
      if (isAuthenticated) {
        emit(const BiometricAuthenticatedState());
      } else {
        emit(const AuthError('Biometric authentication failed'));
      }
    } catch (e) {
      emit(AuthError(e.toString()));
    }
  }

  @override
  Future<void> close() {
    _authenticatedUser = null;
    return super.close();
  }

  // Helper method to clean up error messages
  String _cleanErrorMessage(String error) {
    // Remove generic "Exception: " prefix
    var cleanedError = error.replaceAll('Exception: ', '');

    // for specifc classes like network error, apiexception, serverexception
    if (cleanedError.contains('ApiException(')) {
      final regex = RegExp(r'ApiException\((.*?),');
      final match = regex.firstMatch(cleanedError);
      if (match != null && match.groupCount > 0) {
        cleanedError = match.group(1) ?? cleanedError;
      }
    }

    return cleanedError;
  }
}
