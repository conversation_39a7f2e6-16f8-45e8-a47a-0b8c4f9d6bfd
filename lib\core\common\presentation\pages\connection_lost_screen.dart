import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/connectivity/connectivity_controller.dart';
import 'package:cbrs/core/services/device/device_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ConnectionLostScreen extends StatefulWidget {
  const ConnectionLostScreen({
    super.key,
    this.title = 'No Internet Connection',
    this.message = 'Please check your internet connection and try again.',
    this.buttonText = 'Try Again',
    this.onButtonPressed,
  });
  final String? title;
  final String? message;
  final String? buttonText;
  final VoidCallback? onButtonPressed;

  @override
  _ConnectionLostScreenState createState() => _ConnectionLostScreenState();
}

class _ConnectionLostScreenState extends State<ConnectionLostScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _animateIn();
  }

  Future<void> _animateIn() async {
    await Future.delayed(const Duration(milliseconds: 100));
    if (mounted) {
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _handleRetry() async {
    if (_isLoading) return;

    setState(() => _isLoading = true);

    await Future.delayed(
        const Duration(seconds: 1)); // to simulate/show loading

    try {
      final connectivityController = Get.find<ConnectivityController>();
      await connectivityController.checkConnectivity();

      if (connectivityController.isConnected && mounted) {
        final deviceController = Get.find<DeviceCheckController>();
        deviceController.resetInitialization();
        await deviceController.checkDeviceOnStartup();

        if (mounted) {
          if (context.canPop()) {
            context.pop();
          } else {
            context.go('/');
          }
        }
      }
    } catch (e) {
      if (e.toString().toLowerCase().contains('network') ||
          e.toString().toLowerCase().contains('connection')) {
        // Stay on connection lost screen if it's a network error
        return;
      }
      // For other errors, try to navigate back or to home
      if (mounted) {
        if (context.canPop()) {
          context.pop();
        } else {
          context.go('/');
        }
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.dark.copyWith(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
      ),
      child: Scaffold(
        body: Center(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildIcon(),
                SizedBox(height: 24.h),
                _buildMessage(),
                SizedBox(height: 40.h),
                _buildRetryButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildIcon() {
    return FadeTransition(
      opacity: _animationController,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, -0.2),
          end: Offset.zero,
        ).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutCubic,
          ),
        ),
        child: Image.asset(
          MediaRes.noConnectivity,
          width: 140.w,
          height: 140.h,
        ),
      ),
    );
  }

  Widget _buildMessage() {
    return FadeTransition(
      opacity: Tween<double>(begin: 0, end: 1).animate(
        CurvedAnimation(
          parent: _animationController,
          curve: const Interval(0.3, 1, curve: Curves.easeOut),
        ),
      ),
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, 0.2),
          end: Offset.zero,
        ).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: const Interval(0.3, 1, curve: Curves.easeOutCubic),
          ),
        ),
        child: Column(
          children: [
            Text(
              widget.title!,
              textAlign: TextAlign.center,
              style: GoogleFonts.outfit(
                fontSize: 22.sp,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 12.h),
            Text(
              'Make sure your device is connected to a Wi-Fi network or has mobile data turned on.',
              textAlign: TextAlign.center,
              style: GoogleFonts.outfit(
                fontSize: 15.sp,
                fontWeight: FontWeight.w400,
                color: Colors.black.withOpacity(0.6),
                height: 1.4,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRetryButton() {
    return CustomRoundedBtn(
      btnText: 'Try again',
      onTap: () {
        if (widget.onButtonPressed != null) {
          debugPrint('onButtonPressed called');
          setState(() {
            _isLoading = true;
          });
        } else {
          debugPrint('_handleRetry called');

          _handleRetry();
        }
        // widget.onButtonPressed ?? _handleRetry();
      },
      isLoading: _isLoading,
    );
  }
}
