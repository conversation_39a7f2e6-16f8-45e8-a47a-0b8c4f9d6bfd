import 'dart:io';
import 'package:cbrs/features/identity_verification/data/models/document_upload_response_model.dart';
import 'package:cbrs/features/identity_verification/data/models/identity_document_model.dart';

abstract class IdentityVerificationRemoteDataSource {
  /// Upload identity document with front, back, and selfie photos
  ///
  /// [frontPhoto] - File containing the front side of the document
  /// [backPhoto] - File containing the back side of the document
  /// [selfiePhoto] - File containing the user's selfie
  /// [documentType] - Type of document being uploaded (e.g., 'passport', 'id_card', 'driver_license')
  ///
  /// Returns [DocumentUploadResponseModel] containing upload status and details
  Future<DocumentUploadResponseModel> uploadDocument({
    required File frontPhoto,
    required File backPhoto,
    required File selfiePhoto,
    String documentType = 'id_card',
  });
}
