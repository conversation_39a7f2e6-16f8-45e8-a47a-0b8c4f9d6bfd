import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/core/services/firebase_notification_service/fcm_service.dart';
import 'package:cbrs/features/auth/data/models/user_dto.dart';
import 'package:flutter/services.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:local_auth/local_auth.dart';
import 'package:flutter/foundation.dart';

abstract class AuthLocalDataSource {
  Future<void> cacheUserData(LocalUserDTO user);
  Future<LocalUserDTO?> getCachedUserData();
  Future<void> clearUserData();

  Future<bool> authenticateWithBiometrics();
  Future<void> saveAuthToken(String token);
  Future<String?> getAuthToken();
  Future<void> clearAuthToken();
  Future<void> logout();
  Future<String?> getUserId();
  Future<String?> getDeviceUUID();

  Future<void> saveUserData(LocalUserDTO user);
  Future<bool> isBiometricAvailable();
  Future<BiometricType?> getPreferredBiometric();
}

class SecureAuthLocalDataSourceImpl implements AuthLocalDataSource {
  SecureAuthLocalDataSourceImpl({
    required Box<dynamic> authBox,
    required LocalAuthentication localAuth,
  })  : _authBox = authBox,
        _localAuth = localAuth;
  final Box<dynamic> _authBox;
  final LocalAuthentication _localAuth;

  static const String kUserKey = 'user_data';
  static const String kTokenKey = 'auth_token';

  @override
  Future<void> cacheUserData(LocalUserDTO user) async {
    try {
      debugPrint('\n=== CACHING USER DATA ===');
      final userData = user.toJson();


      await _authBox.put(kUserKey, userData);


      // If data is nested, flatten it
      if (userData.containsKey('data')) {
        final data = userData['data'] as Map<String, dynamic>;
        userData
          ..remove('data')
          ..addAll(data);
      }

      // Ensure token is included
      if (!userData.containsKey('token') &&
          userData.containsKey('accessToken')) {
        userData['token'] = userData['accessToken'];
      }

      // userData['isEmailVerified'] = false;
      // userData['email'] = '';

      await _authBox.put(kUserKey, userData);
    } catch (e) {
      throw CacheException(message: 'Failed to cache user data: $e');
    }
  }

  @override
  Future<LocalUserDTO?> getCachedUserData() async {
    debugPrint('\n=== GETTING CACHED USER DATA ===');
    try {
      final userData = await _authBox.get(kUserKey);
      final token = await _authBox.get(kTokenKey);

      if (userData == null) {
        debugPrint('No cached user data found');
        return null;
      }

      final userDataMap = Map<String, dynamic>.from(userData as Map);

      if (token != null) {
        userDataMap['token'] = token;
      }

      debugPrint('Parsed user data map: userDataMap');
      final dto = LocalUserDTO.fromJson(userDataMap);

      return dto;
    } catch (e) {
      throw CacheException(message: 'Failed to get cached  user data: $e');
    }
  }

  @override
  Future<void> saveAuthToken(String token) async {
    try {
      await _authBox.put(kTokenKey, token);
    } catch (e) {
      throw CacheException(message: 'Failed to save auth token: $e');
    }
  }

  @override
  Future<String?> getAuthToken() async {
    // debugPrint('\n=== GETTING AUTH TOKEN ===');
    try {
      final token = await _authBox.get(kTokenKey);
      // debugPrint('Token exists: ${token != null}');
      // debugPrint('=== GET AUTH TOKEN COMPLETED ttokne value ::  ===\n');
      return token?.toString();
    } catch (e) {
      throw CacheException(message: 'Failed to get auth token: $e');
    }
  }

  @override
  Future<BiometricType?> getPreferredBiometric() async {
    try {
      final canCheckBiometrics = await _localAuth.canCheckBiometrics;
      final canAuthenticate = await _localAuth.isDeviceSupported();

      if (!canCheckBiometrics || !canAuthenticate) {
        debugPrint('❌ Device cannot check biometrics or not supported');
        return null;
      }

      final availableBiometrics = await _localAuth.getAvailableBiometrics();
      debugPrint('📱 Available biometrics: $availableBiometrics');

      if (availableBiometrics.isEmpty) {
        debugPrint('⚠️ No biometrics available');
        return null;
      }

      // Check for face first, then fingerprint
      if (availableBiometrics.contains(BiometricType.face)) {
        debugPrint('✓ Face ID available');
        return BiometricType.face;
      } else if (availableBiometrics.contains(BiometricType.fingerprint)) {
        debugPrint('✓ Fingerprint available');
        return BiometricType.fingerprint;
      } else if (availableBiometrics.contains(BiometricType.strong)) {
        debugPrint('✓ Strong biometric available');
        return BiometricType
            .fingerprint; // Default to fingerprint UI for strong
      }

      debugPrint('⚠️ No supported biometric type found');
      return null;
    } catch (e) {
      debugPrint('❌ Error getting preferred biometric: $e');
      return null;
    }
  }

  @override
  Future<bool> authenticateWithBiometrics() async {
    try {
      final canCheckBiometrics = await _localAuth.canCheckBiometrics;
      final canAuthenticate = await _localAuth.isDeviceSupported();

      if (!canCheckBiometrics || !canAuthenticate) {
        throw const BiometricException(
          message: 'Biometric authentication not available on this device',
        );
      }

      final preferredBiometric = await getPreferredBiometric();
      if (preferredBiometric == null) {
        throw const BiometricException(
          message: 'No biometrics enrolled on this device',
        );
      }

      final reason = preferredBiometric == BiometricType.face
          ? 'Please scan your face to access your account'
          : 'Please scan your fingerprint to access your account';

      return await _localAuth.authenticate(
        localizedReason: reason,
        options: const AuthenticationOptions(
          stickyAuth: true,
          biometricOnly: true,
        ),
      );
    } on PlatformException catch (e) {
      throw BiometricException(
        message: 'Biometric error: ${e.message}',
      );
    } catch (e) {
      throw BiometricException(message: e.toString());
    }
  }

  @override
  Future<void> clearUserData() async {
    try {
      await _authBox.delete(kUserKey);
    } catch (e) {
      throw CacheException(message: 'Failed to clear user data: $e');
    }
  }

  @override
  Future<void> clearAuthToken() async {
    try {
      await _authBox.delete(kTokenKey);
    } catch (e) {
      throw CacheException(message: 'Failed to clear auth token: $e');
    }
  }

  @override
  Future<void> logout() async {
    try {
      await Future.wait([
        _authBox.delete(kUserKey),
        _authBox.delete(kTokenKey),
        updateUserStatus(isOnline: false),
      ]);
    } catch (e) {
      throw CacheException(message: 'Failed to logout: $e');
    }
  }

  @override
  Future<String?> getUserId() async {
    try {
      final userData = await _authBox.get(kUserKey);
      if (userData == null) {
        debugPrint('⚠️ No user data found in storage');
        return null;
      }

      final userDataMap = Map<String, dynamic>.from(userData as Map);
      final userId =
          userDataMap['id'] as String? ?? userDataMap['_id'] as String?;

      return userId;
    } catch (e) {
      debugPrint('❌ Error getting user ID: $e');
      throw CacheException(message: 'Failed to get user ID: $e');
    }
  }

  @override
  Future<String?> getDeviceUUID() async {
    try {
      final userData = await _authBox.get(kUserKey);
      if (userData == null) {
        debugPrint('⚠️ No user data found in storage');
        return null;
      }

      final userDataMap = Map<String, dynamic>.from(userData as Map);
      final deviceUUID = userDataMap['deviceUUID'] as String? ??
          userDataMap['deviceUUID'] as String?;

      return deviceUUID;
    } catch (e) {
      debugPrint('❌ Error getting user deviceUUID: $e');
      throw CacheException(message: 'Failed to get user ID: $e');
    }
  }

  @override
  Future<void> saveUserData(LocalUserDTO user) async {
    try {

      debugPrint('\n=== SAVING USER DATA ===');
      final userData = user.toJson();

      // If data is nested, flatten it
      if (userData.containsKey('data')) {
        final data = userData['data'] as Map<String, dynamic>;
        userData
          ..remove('data')
          ..addAll(data);
      }

      // Ensure ID is properly stored
      if (userData['_id'] != null) {
        userData['id'] = userData['_id']; // Backup _id as id
      } else if (userData['id'] == null) {
        debugPrint('⚠️ Warning: No ID found in user data');
      }

      // Handle token
      var token = userData['token'] as String?;
      if (token == null && userData.containsKey('accessToken')) {
        token = userData['accessToken'] as String?;
        userData['token'] = token;
      }

      // Save token separately if present
      if (token != null) {
        await _authBox.put(kTokenKey, token);
        debugPrint('✓ Auth token saved separately');
      }

    

      await _authBox.put(kUserKey, userData);
      // debugPrint(
      //     '✓ User data saved successfullhdshsy ${userData['memberLevel']}');
      // debugPrint('Saved user ID: ${userData ?? userData['_id']}');
    } catch (e) {
      debugPrint('❌ Failed to save user data: $e');
      throw CacheException(message: 'Failed to save user data: $e');
    }
  }

  @override
  Future<bool> isBiometricAvailable() async {
    try {
      debugPrint('\n=== Checking Biometric Availability ===');

      final canCheckBiometrics = await _localAuth.canCheckBiometrics;
      debugPrint('Can check biometrics: $canCheckBiometrics');

      final canAuthenticate = await _localAuth.isDeviceSupported();
      debugPrint('Device supported: $canAuthenticate');

      if (!canCheckBiometrics || !canAuthenticate) {
        debugPrint('❌ Basic biometric checks failed');
        return false;
      }

      final availableBiometrics = await _localAuth.getAvailableBiometrics();
      debugPrint('Available biometrics: $availableBiometrics');

      final hasValidBiometric =
          availableBiometrics.contains(BiometricType.face) ||
              availableBiometrics.contains(BiometricType.fingerprint) ||
              availableBiometrics.contains(BiometricType.strong);

      debugPrint('Has valid biometric: $hasValidBiometric');
      debugPrint('=== Biometric Check Complete ===\n');

      return hasValidBiometric;
    } catch (e) {
      debugPrint('❌ Error checking biometric availability: $e');
      return false;
    }
  }
}
