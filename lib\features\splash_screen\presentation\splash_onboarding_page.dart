import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/features/link_acccount/presentation/views/index.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:video_player/video_player.dart';
import 'package:go_router/go_router.dart';

class SplashOnboardingPage extends StatefulWidget {
  const SplashOnboardingPage({super.key});

  @override
  State<SplashOnboardingPage> createState() => _SplashOnboardingPageState();
}

class _SplashOnboardingPageState extends State<SplashOnboardingPage> {
  final PageController _controller = PageController();
  late VideoPlayerController _videoPlayerController;
  bool _isVideoInitialized = false;
  int _currentIndex = 0;

  final List<String> onboardingVideos = [
    MediaRes.chatTransferVideo,
    MediaRes.loanVideo,
    MediaRes.myBankAccountVideo,
    MediaRes.remittanceRenderVideo,
    MediaRes.walletTransferVideo,
  ];

  @override
  void initState() {
    super.initState();
    _initializeVideo(_currentIndex);
  }

  Future<void> _initializeVideo(int index) async {
    _videoPlayerController =
        VideoPlayerController.asset(onboardingVideos[index]);

    await _videoPlayerController.initialize();
    setState(() {});
    _videoPlayerController
      ..setVolume(0)
      ..play();

    _videoPlayerController.addListener(() {
      if (_videoPlayerController.value.position >=
              _videoPlayerController.value.duration &&
          _videoPlayerController.value.isPlaying) {
        if (_currentIndex < onboardingVideos.length - 1) {
          _controller.nextPage(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        } else {
          context.goNamed(AppRouteName.onboarding);
        }
      }
    });

  

    setState(() {
      _isVideoInitialized = true;
    });
  }

  Future<void> _onPageChanged(int index) async {
    setState(() {
      _isVideoInitialized = false;
    });

    await _videoPlayerController.pause();
    await _videoPlayerController.dispose();

    _currentIndex = index;
    await _initializeVideo(index);
  }

  Future<void> _nextPage() async {
    if (_currentIndex < onboardingVideos.length - 1) {
      _controller.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      await _videoPlayerController.pause();
      context.goNamed(AppRouteName.onboarding);
    }
  }

  Future<void> _skip() async {
    await _videoPlayerController.pause();
    context.goNamed(AppRouteName.onboarding);
  }

  @override
  void dispose() {
    _videoPlayerController.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: Scaffold(
        body: Container(
          width: double.infinity,
          color: const Color(0xFFE8E8E8),

          // decoration: const BoxDecoration(
          //   gradient: LinearGradient(
          //     colors: [
          //       Color.fromRGBO(105, 172, 92, 0.04),
          //       Color.fromRGBO(8, 89, 5, 0.08),
          //     ],
          //     stops: [0, 1],
          //   ),
          // ),
          child: Stack(
            children: [
              Positioned.fill(
                child: PageView.builder(
                  controller: _controller,
                  itemCount: onboardingVideos.length,
                  onPageChanged: _onPageChanged,
                  itemBuilder: (context, index) {
                    return Center(
                      child: _isVideoInitialized
                          ? AspectRatio(
                              aspectRatio:
                                  _videoPlayerController.value.aspectRatio,
                              child: VideoPlayer(_videoPlayerController),
                            )
                          : const CircularProgressIndicator(),
                    );
                  },
                ),
              ),
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(36),
                      topRight: Radius.circular(36),
                    ),
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(
                          onboardingVideos.length,
                          (index) => Container(
                            margin: const EdgeInsets.symmetric(horizontal: 3),
                            width: 10,
                            height: 10,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: _currentIndex == index
                                  ? Theme.of(context).primaryColor
                                  : Theme.of(context).secondaryHeaderColor,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 12),
                      CustomBuildText(
                        text:
                            'Send and receive money effortlessly across borders. Enjoy secure, quick, and hassle-free remittance transfers with just a few taps.',
                        color: Colors.black.withOpacity(0.4),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 12),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          TextButton(
                            onPressed: _skip,
                            child: Text(
                              'Skip',
                              style: GoogleFonts.outfit(
                                fontSize: 16,
                                color: Theme.of(context).primaryColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          ElevatedButton(
                            onPressed: _nextPage,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Theme.of(context).primaryColor,
                              foregroundColor: Colors.white,
                            ),
                            child: Text(
                              'Next',
                              style: GoogleFonts.outfit(
                                fontSize: 16,
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
