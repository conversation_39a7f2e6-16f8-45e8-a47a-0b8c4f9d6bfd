import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

@immutable
class IdentityDocument extends Equatable {
  const IdentityDocument({
    required this.id,
    required this.userId,
    required this.documentType,
    required this.frontImageUrl,
    required this.backImageUrl,
    required this.selfieImageUrl,
    required this.status,
    required this.uploadedAt,
    this.verifiedAt,
    this.rejectedAt,
    this.rejectionReason,
    this.metadata,
  });

  final String id;
  final String userId;
  final String documentType;
  final String frontImageUrl;
  final String backImageUrl;
  final String selfieImageUrl;
  final DocumentStatus status;
  final DateTime uploadedAt;
  final DateTime? verifiedAt;
  final DateTime? rejectedAt;
  final String? rejectionReason;
  final Map<String, dynamic>? metadata;

  @override
  List<Object?> get props => [
        id,
        userId,
        documentType,
        frontImageUrl,
        backImageUrl,
        selfieImageUrl,
        status,
        uploadedAt,
        verifiedAt,
        rejectedAt,
        rejectionReason,
        metadata,
      ];

  IdentityDocument copyWith({
    String? id,
    String? userId,
    String? documentType,
    String? frontImageUrl,
    String? backImageUrl,
    String? selfieImageUrl,
    DocumentStatus? status,
    DateTime? uploadedAt,
    DateTime? verifiedAt,
    DateTime? rejectedAt,
    String? rejectionReason,
    Map<String, dynamic>? metadata,
  }) {
    return IdentityDocument(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      documentType: documentType ?? this.documentType,
      frontImageUrl: frontImageUrl ?? this.frontImageUrl,
      backImageUrl: backImageUrl ?? this.backImageUrl,
      selfieImageUrl: selfieImageUrl ?? this.selfieImageUrl,
      status: status ?? this.status,
      uploadedAt: uploadedAt ?? this.uploadedAt,
      verifiedAt: verifiedAt ?? this.verifiedAt,
      rejectedAt: rejectedAt ?? this.rejectedAt,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      metadata: metadata ?? this.metadata,
    );
  }

  // Helper methods
  bool get isPending => status == DocumentStatus.pending;
  bool get isVerified => status == DocumentStatus.verified;
  bool get isRejected => status == DocumentStatus.rejected;
  bool get isProcessing => status == DocumentStatus.processing;

  // Empty document singleton
  static final empty = IdentityDocument(
    id: '',
    userId: '',
    documentType: '',
    frontImageUrl: '',
    backImageUrl: '',
    selfieImageUrl: '',
    status: DocumentStatus.pending,
    uploadedAt: DateTime.now(),
  );
}

enum DocumentStatus {
  pending,
  processing,
  verified,
  rejected,
}

extension DocumentStatusExtension on DocumentStatus {
  String get displayName {
    switch (this) {
      case DocumentStatus.pending:
        return 'Pending';
      case DocumentStatus.processing:
        return 'Processing';
      case DocumentStatus.verified:
        return 'Verified';
      case DocumentStatus.rejected:
        return 'Rejected';
    }
  }

  String get value {
    switch (this) {
      case DocumentStatus.pending:
        return 'pending';
      case DocumentStatus.processing:
        return 'processing';
      case DocumentStatus.verified:
        return 'verified';
      case DocumentStatus.rejected:
        return 'rejected';
    }
  }

  static DocumentStatus fromString(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return DocumentStatus.pending;
      case 'processing':
        return DocumentStatus.processing;
      case 'verified':
        return DocumentStatus.verified;
      case 'rejected':
        return DocumentStatus.rejected;
      default:
        return DocumentStatus.pending;
    }
  }
}
