import 'package:flutter/foundation.dart';

class GetInitialNames {
  static String getInitials(String name,
      {bool showInitialDebug = false, bool shouldReturnLowerCase = false}) {
    if (name.isEmpty) {
      return '?';
    }
    if (showInitialDebug) debugPrint("getInitials ⛑️ $name");

    final nameParts = name.trim().split(' ');
    if (showInitialDebug) debugPrint("lenght  ⛑️ ${nameParts.length}");
    if (nameParts.length >= 2) {
      if (shouldReturnLowerCase)
        return '${nameParts[0][0].toLowerCase()}${nameParts[1][0].toLowerCase()}';
      return '${nameParts[0][0]}${nameParts[1][0]}'.toUpperCase();
    }
    if (shouldReturnLowerCase)
      return name.substring(0, name.length >= 2 ? 2 : 1).toLowerCase();

    return name.substring(0, name.length >= 2 ? 2 : 1).toUpperCase();
  }
}
