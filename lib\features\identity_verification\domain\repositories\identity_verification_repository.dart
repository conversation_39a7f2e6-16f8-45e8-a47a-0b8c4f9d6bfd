import 'dart:io';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/identity_verification/domain/entities/document_upload_response.dart';
import 'package:cbrs/features/identity_verification/domain/entities/identity_document.dart';

abstract class IdentityVerificationRepository {
  /// Upload identity document with front, back, and selfie photos
  ///
  /// [frontPhoto] - File containing the front side of the document
  /// [backPhoto] - File containing the back side of the document
  /// [selfiePhoto] - File containing the user's selfie
  /// [documentType] - Type of document being uploaded (e.g., 'passport', 'id_card', 'driver_license')
  ///
  /// Returns [DocumentUploadResponse] containing upload status and details
  ResultFuture<DocumentUploadResponse> uploadDocument({
    required File frontPhoto,
    required File backPhoto,
    required File selfiePhoto,
    String documentType = 'id_card',
  });
}
