import 'package:flutter/material.dart';
import 'package:flutter_contacts/flutter_contacts.dart';

Future<List<dynamic>> getContacts(BuildContext context) async {
  bool permissionGranted = await FlutterContacts.requestPermission();

  if (!permissionGranted) {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Permission Required'),
        content: Text(
          'This app needs access to your contacts to display connect users',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              permissionGranted = await FlutterContacts.requestPermission();
              if (permissionGranted) {
                getContacts(context);
              }
            },
            child: Text('Try Again'),
          ),
        ],
      ),
    );

    if (!permissionGranted) {
      return [];
    }
  }

  // if permission is granted
  final contacts = await FlutterContacts.getContacts(
    withProperties: true,
    withPhoto: true,
    withThumbnail: true,
  );

  debugPrint("{phone numer fetched ${contacts.length}}");

  return contacts;
}
