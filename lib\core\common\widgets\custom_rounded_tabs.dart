import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

class CustomRoundedTabs extends StatelessWidget {
  const CustomRoundedTabs({
    required this.onTap,
    required this.selectedTab,
    required this.tabList,
    super.key,
  });
  final void Function(String) onTap;
  final String selectedTab;
  final List<String> tabList;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          // height: 48,
          padding: const EdgeInsets.all(2),
          clipBehavior: Clip.antiAlias,
          decoration: ShapeDecoration(
            color: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(50),
              // side: BorderSide(color: Colors.black)
            ),
            shadows: [
              BoxShadow(
                color: Colors.black.withOpacity(0.06),
                blurRadius: 24,
              ),
            ],
          ),
          child: Container(
            decoration: ShapeDecoration(
              color: const Color(0xFFF9F9F9),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(50),
                // side: BorderSide(color: Colors.black)
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: double.infinity,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: tabList
                        .map(
                          (tabLabel) => _buildTab(context, tabName: tabLabel),
                        )
                        .toList(),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTab(
    BuildContext context, {
    required String tabName,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: () => onTap(tabName),
        child: Container(
          // height: 38.h,
          padding: EdgeInsets.symmetric(vertical: 5.h),
          clipBehavior: Clip.antiAlias,
          decoration: ShapeDecoration(
            color: selectedTab == tabName
                ? Theme.of(context).primaryColor
                : Colors.transparent,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(28),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomBuildText(
                text: tabName,
                caseType: 'default',
                // " ${tabName.substring(0, 1).toUpperCase()}${tabName.substring(1).toLowerCase()}",
                style: GoogleFonts.outfit(
                  color: selectedTab == tabName
                      ? Colors.white
                      : Theme.of(context).primaryColor,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  height: 1.75,
                  letterSpacing: -0.32,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
