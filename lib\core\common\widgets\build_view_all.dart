import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BuildViewAll extends StatelessWidget {
  const BuildViewAll({required this.onTap, super.key});
  final void Function()? onTap;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 4.h),
        decoration: const BoxDecoration(),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'View All',
              style: GoogleFonts.outfit(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).primaryColor,
              ),
            ),
            SizedBox(width: 4.w),

            Image.asset(MediaRes.forwardIcon, width: 20,   color: Theme.of(context).primaryColor,),
            // Icon(
            //   Icons.arrow_forward_ios_rounded,
            //   size: 20.h,
            //   color: Theme.of(context).primaryColor,
            // ),
          ],
        ),
      ),
    );
  }
}
