import 'package:intl/intl.dart';

class NotificationsDateTime {
  static String timeAgo(DateTime dateTime) {
    final Duration difference = DateTime.now().difference(dateTime);

    if (difference.inSeconds < 0) {
      return 'on ${_monthName(dateTime.month)} ${dateTime.day}';
    }
    if (difference.inSeconds < 60) {
      return '${difference.inSeconds} sec${difference.inSeconds == 1 ? '' : 's'} ago';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} min${difference.inMinutes == 1 ? '' : 's'} ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} hr${difference.inHours == 1 ? '' : 's'} ago';
    } 
    else if (difference.inDays == 1) {
      return 'Yesterday at ${DateFormat('hh:mm a').format(dateTime)}';

    } else if (difference.inDays < 365) {
      // Format as "on Jan 3"
      return 'on ${_monthName(dateTime.month)} ${dateTime.day} at ${DateFormat('hh:mm a').format(dateTime)}';
    } else {
      // Format as "on Jan 3, 2024"
      return 'on ${_monthName(dateTime.month)} ${dateTime.day}, ${dateTime.year}';
    }
  }

  static String _monthName(int month) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return months[month - 1];
  }
}
