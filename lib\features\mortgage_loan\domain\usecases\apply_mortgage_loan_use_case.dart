import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/mortgage_loan/domain/entities/mortgage_loan_application.dart';
import 'package:cbrs/features/mortgage_loan/domain/repositories/mortgage_bank_repository.dart';
import 'package:equatable/equatable.dart';

class ApplyMortgageLoanUseCase extends UsecaseWithParams<
    MortgageLoanApplication, MortgageApplyLoanParams> {
  const ApplyMortgageLoanUseCase(this._repositores);
  final MortgageBankRepository _repositores;

  @override
  ResultFuture<MortgageLoanApplication> call(MortgageApplyLoanParams params) {
    return _repositores.applyMortgageLoan(
      loanId: params.loanId,
      // memberId: params.memberId,
      productId: params.productId,
      upfrontPaymentPercentage: params.upfrontPaymentPercentage,
      isAutoRepay: params.isAutoRepay,
    );
  }
}

class MortgageApplyLoanParams extends Equatable {
  const MortgageApplyLoanParams({
    required this.loanId,
    required this.memberId,
    required this.productId,
    required this.upfrontPaymentPercentage,
    this.isAutoRepay = false,
  });

  final String loanId;
  final String memberId;
  final String productId;
  final String upfrontPaymentPercentage;
  final bool isAutoRepay;

  @override
  List<Object> get props =>
      [loanId, memberId, productId, upfrontPaymentPercentage, isAutoRepay];
}
