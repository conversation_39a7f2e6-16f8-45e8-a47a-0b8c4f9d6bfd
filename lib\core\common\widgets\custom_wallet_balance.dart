import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

class CustomWalletBalance extends StatefulWidget {
  const CustomWalletBalance({
    required this.walletBalance,
    required this.isBirrBalance,
    this.alwaysShowETBColor = false,
    super.key,
    this.formattedBalance = '',
    this.balanceLabel = 'Wallet Balance',
  });
  final String formattedBalance;
  final double walletBalance;
  final bool isBirrBalance;
  final bool alwaysShowETBColor;
  final String balanceLabel;

  @override
  State<CustomWalletBalance> createState() => _CustomWalletBalanceState();
}

class _CustomWalletBalanceState extends State<CustomWalletBalance> {
  bool showBalance = false;

  void handleShowBalance() {
    setState(() {
      showBalance = !showBalance;
    });
  }

  @override
  Widget build(BuildContext context) {
    final balance = widget.formattedBalance.isNotEmpty
        ? widget.formattedBalance
        : AppMapper.safeFormattedNumberWithDecimal(widget.walletBalance);
    return Center(
      child: GestureDetector(
        onTap: handleShowBalance,
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: 16.w,
            vertical: 8.h,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(32.r),
            color: widget.alwaysShowETBColor ? LightModeTheme().primaryColorBirr.withOpacity(0.2) : Theme.of(context).secondaryHeaderColor,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "${widget.balanceLabel}:",
                style: GoogleFonts.outfit(
                  fontSize: 14.sp,
                  color: Colors.black87,
                ),
              ),
              Flexible(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CustomBuildText(
                      text: showBalance
                          ? widget.isBirrBalance
                              ? ' $balance'
                              : ' $balance'
                          : '  ********',
                      style: GoogleFonts.outfit(
                        fontSize: showBalance ? 16.sp : 16.sp,
                        fontWeight: FontWeight.w700,
                        color: Colors.black,
                      ),
                    ),
                  ],
                ),
              ),
              if (widget.isBirrBalance)
                Padding(
                  padding: const EdgeInsets.only(left: 4, top: 1.5),
                  child: CustomBuildText(
                    text: 'ETB',
                    caseType: 'all',
                    style: GoogleFonts.outfit(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w700,
                      color: Colors.black,
                    ),
                  ),
                ),
              Padding(
                padding: const EdgeInsets.only(left: 8),
                child: Image.asset(
                  showBalance ? MediaRes.eyeClose : MediaRes.eyeOpen,
                  width: 16,
                  height: 16,
                  color: Colors.black,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
