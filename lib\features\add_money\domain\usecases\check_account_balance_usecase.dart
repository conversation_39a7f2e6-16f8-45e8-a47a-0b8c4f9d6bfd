import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/add_money/domain/entities/linked_account.dart';
import 'package:cbrs/features/add_money/domain/repositories/add_money_repository.dart';
import 'package:equatable/equatable.dart';

class CheckAccountBalance
    extends UsecaseWithParams<LinkedAccount, CheckAccountBalanceParams> {
  final AddMoneyRepository _repository;

  const CheckAccountBalance(this._repository);

  @override
  ResultFuture<LinkedAccount> call(CheckAccountBalanceParams params) async {
    return _repository.checkAccountBalance(
      bankId: params.bankId,
      accountNumber: params.accountNumber,
    );
  }
}

class CheckAccountBalanceParams extends Equatable {
  final String bankId;
  final String accountNumber;

  const CheckAccountBalanceParams({
    required this.bankId,
    required this.accountNumber,
  });

  @override
  List<Object?> get props => [bankId, accountNumber];
}
