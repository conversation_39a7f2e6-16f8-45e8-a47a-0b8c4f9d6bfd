import 'package:equatable/equatable.dart';
import '../../domain/entities/linked_account.dart';

class LinkedAccountResponseModel extends LinkedAccount {
  const LinkedAccountResponseModel({
    required super.id,
    required super.bank,
    required super.accountNumber,
    required super.member,
    required super.linkingCode,
    required super.status,
    required super.enabled,
    required super.createdAt,
    required super.updatedAt,
    required super.accountBalance,
  });

  factory LinkedAccountResponseModel.fromJson(Map<String, dynamic> json) {
    return LinkedAccountResponseModel(
      id: json['_id']?.toString() ?? '',
      bank: BankModel.fromJson(json['bank'] as Map<String, dynamic>? ?? {}),
      accountNumber: json['accountNumber']?.toString() ?? '',
      member:
          MemberModel.fromJson(json['member'] as Map<String, dynamic>? ?? {}),
      linkingCode: (json['linkingCode'] as num?)?.toInt() ?? 0,
      status: json['status']?.toString() ?? '',
      enabled: json['enabled'] as bool? ?? false,
      createdAt: DateTime.parse(
          json['createdAt']?.toString() ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(
          json['updatedAt']?.toString() ?? DateTime.now().toIso8601String()),
      accountBalance: (json['accountBalance'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() => {
        '_id': id,
        'bank': (bank as BankModel).toJson(),
        'accountNumber': accountNumber,
        'member': (member as MemberModel).toJson(),
        'linkingCode': linkingCode,
        'status': status,
        'enabled': enabled,
        'createdAt': createdAt.toIso8601String(),
        'updatedAt': updatedAt.toIso8601String(),
        'accountBalance': accountBalance,
      };
}

class BankModel extends Bank {
  const BankModel({
    required super.id,
    required super.name,
    required super.logo,
  });

  factory BankModel.fromJson(Map<String, dynamic> json) {
    return BankModel(
      id: json['_id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      logo: json['logo']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
        '_id': id,
        'name': name,
        'logo': logo,
      };
}

class MemberModel extends Member {
  const MemberModel({
    required super.id,
    required super.firstName,
    required super.middleName,
    required super.lastName,
    required super.email,
  });

  factory MemberModel.fromJson(Map<String, dynamic> json) {
    return MemberModel(
      id: json['_id']?.toString() ?? '',
      firstName: json['firstName']?.toString() ?? '',
      middleName: json['middleName']?.toString() ?? '',
      lastName: json['lastName']?.toString() ?? '',
      email: json['email']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
        '_id': id,
        'firstName': firstName,
        'middleName': middleName,
        'lastName': lastName,
        'email': email,
      };
}

class PaginatedLinkedAccountsResponseModel extends PaginatedLinkedAccounts {
  const PaginatedLinkedAccountsResponseModel({
    required super.docs,
    required super.totalDocs,
    required super.limit,
    required super.totalPages,
    required super.page,
    required super.pagingCounter,
    required super.hasPrevPage,
    required super.hasNextPage,
    super.prevPage,
    super.nextPage,
  });

  factory PaginatedLinkedAccountsResponseModel.fromJson(
      Map<String, dynamic> json) {
    final data = json['data'] as Map<String, dynamic>? ?? {};
    return PaginatedLinkedAccountsResponseModel(
      docs: (data['docs'] as List?)
              ?.map((doc) => LinkedAccountResponseModel.fromJson(
                  doc as Map<String, dynamic>))
              .toList() ??
          [],
      totalDocs: (data['totalDocs'] as num?)?.toInt() ?? 0,
      limit: (data['limit'] as num?)?.toInt() ?? 0,
      totalPages: (data['totalPages'] as num?)?.toInt() ?? 0,
      page: (data['page'] as num?)?.toInt() ?? 0,
      pagingCounter: (data['pagingCounter'] as num?)?.toInt() ?? 0,
      hasPrevPage: data['hasPrevPage'] as bool? ?? false,
      hasNextPage: data['hasNextPage'] as bool? ?? false,
      prevPage: (data['prevPage'] as num?)?.toInt(),
      nextPage: (data['nextPage'] as num?)?.toInt(),
    );
  }

  Map<String, dynamic> toJson() => {
        'data': {
          'docs': docs
              .map((doc) => (doc as LinkedAccountResponseModel).toJson())
              .toList(),
          'totalDocs': totalDocs,
          'limit': limit,
          'totalPages': totalPages,
          'page': page,
          'pagingCounter': pagingCounter,
          'hasPrevPage': hasPrevPage,
          'hasNextPage': hasNextPage,
          'prevPage': prevPage,
          'nextPage': nextPage,
        }
      };
}
