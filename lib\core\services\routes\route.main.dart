part of 'route.dart';

final navigationService = NavigationService(
  hiveBoxManager: HiveBoxManager(),
  authLocalDataSource: sl<AuthLocalDataSource>(),
);

final GoRouter router = GoRouter(
  navigatorKey: mainNavigatorKey,
  initialLocation: '/splashLoadingScreen',
  redirect: (context, state) {
    debugPrint('Redirecting to from the main rout 🦲 ${state.matchedLocation}');
    return navigationService.handleRedirect(state.matchedLocation);
  },
  routes: [
    GoRoute(
      path: '/splashLoadingScreen',
      name: '/splashLoadingScreen',
      pageBuilder: (context, state) => _pageBuilder(
        const SplashLoadingScreen(),
        state,
      ),
    ),
    GoRoute(
      path: AppRouteName.forceUpdate,
      name: AppRouteName.forceUpdate,
      pageBuilder: (context, state) => _pageBuilder(
        InAppUpdateView(),
        state,
      ),
    ),
    GoRoute(
      path: AppRouteName.onboarding,
      name: AppRouteName.onboarding,
      pageBuilder: (context, state) => _pageBuilder(
        BlocProvider.value(
          value: sl<OnBoardingCubit>(),
          child: const OnBoardingScreen(),
        ),
        state,
      ),
    ),
    GoRoute(
      path: AppRouteName.splashOnboardig,
      name: AppRouteName.splashOnboardig,
      pageBuilder: (context, state) => _pageBuilder(
        const SplashOnboardingPage(),
        state,
      ),
    ),
    GoRoute(
      path: AppRouteName.tokenDeviceLogin,
      name: AppRouteName.tokenDeviceLogin,
      pageBuilder: (context, state) => _pageBuilder(
        BlocProvider.value(
          value: sl<AuthBloc>(),
          child: const TokenDeviceLoginScreen(),
        ),
        state,
      ),
    ),
    GoRoute(
      path: '/connection-lost',
      name: '/connection-lost',
      pageBuilder: (context, state) {
        return _pageBuilder(
          const ConnectionLostScreen(),
          state,
        );
      },
    ),
    GoRoute(
      name: AppRouteName.forgotPin,
      path: '/forgot-pin',
      pageBuilder: (context, state) => _pageBuilder(
        BlocProvider(
          create: (context) => sl<AuthBloc>(),
          child: const ForgotPasswordScreen(),
        ),
        state,
      ),
    ),
    GoRoute(
      name: AppRouteName.videoCall,
      path: AppRouteName.videoCall,
      builder: (context, state) {
        final extra = state.extra as Map<String, dynamic>;

        final receiverId = extra['receiverId']?.toString() ?? '';
        final senderId = extra['senderId']?.toString() ?? '';
        final receiverFirstName = extra['receiverFirstName']?.toString() ?? '';
        final receiverLastName = extra['receiverLastName']?.toString() ?? '';
        final receiverAvatar = extra['receiverAvatar']?.toString() ?? '';
        final isIncoming = extra['isIncoming'] as bool? ?? false;

        return BlocProvider(
          create: (context) => sl<VideoCallBloc>(),
          child: VideoCallScreen(
            receiverId: receiverId,
            senderId: senderId,
            receiverFirstName: receiverFirstName,
            receiverLastName: receiverLastName,
            receiverAvatar: receiverAvatar,
            isIncoming: isIncoming,
          ),
        );
      },
    ),
    authRoutes,
    ShellRoute(
      builder: (context, state, child) => MultiBlocProvider(
        providers: [
          BlocProvider.value(
            value: sl<AuthBloc>(),
          ),
        ],
        child: BlocListener<AuthBloc, AuthState>(
          listener: (context, state) async {},
          child: child,
        ),
      ),
      routes: [
        GoRoute(
          path: AppRouteName.home,
          pageBuilder: (context, state) => _pageBuilder(
            const MainPage(),
            state,
          ),
          routes: _protectedRoutes,
        ),
      ],
    ),
  ],
  errorBuilder: (context, state) => const ErrorScreen(),
);

CustomTransitionPage<dynamic> _pageBuilder(Widget page, GoRouterState state) {
  return CustomTransitionPage(
    key: state.pageKey,
    child: page,
    transitionsBuilder: (context, animation, secondaryAnimation, child) {
      // Shared element transition for logo
      if (state.matchedLocation == AppRouteName.signUp ||
          state.matchedLocation == AppRouteName.signIn) {
        return FadeTransition(
          opacity: animation,
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 0.1),
              end: Offset.zero,
            ).animate(
              CurvedAnimation(
                parent: animation,
                curve: Curves.easeOutCubic,
              ),
            ),
            child: child,
          ),
        );
      }

      // Default transition
      return FadeTransition(
        opacity: CurveTween(curve: Curves.easeInOut).animate(animation),
        child: child,
      );
    },
    transitionDuration: const Duration(milliseconds: 400),
  );
}
