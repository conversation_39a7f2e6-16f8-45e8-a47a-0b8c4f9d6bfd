import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomComingSoon extends StatelessWidget {
  const CustomComingSoon({required this.message, this.cardIcon, super.key});

  final String message;
  final String? cardIcon;
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
      child: Center(
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.06),
                blurRadius: 24,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                cardIcon ?? 'assets/images/empty_transaction_screen_img.png',
                width: 60,
              ),
              const SizedBox(
                height: 16,
              ),
              const CustomBuildText(
                text: 'Coming Soon',
                fontWeight: FontWeight.bold,
                fontSize: 24,
              ),
              SizedBox(
                height: 16.w,
              ),
              CustomBuildText(
                text: message,
                // 'Mini Statement is coming soon! Easily view your recent transactions — stay tuned for the update.',
                textAlign: TextAlign.center,
                caseType: 'deffault',
              ),
            ],
          ),
        ),
      ),
    );
  }
}
