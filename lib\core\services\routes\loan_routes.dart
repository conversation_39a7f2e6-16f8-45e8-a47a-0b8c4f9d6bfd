part of 'route.dart';

final loanRoutes = [
  GoRoute(
    name: AppRouteName.carLoan,
    path: 'car-loan',
    pageBuilder: (context, state) => _pageBuilder(
      BlocProvider(
        create: (context) => sl<CarLoanBloc>(),
        child: CarLoanPage(
          isGuest: state.extra is Map
              ? (state.extra as Map)['isGuest'] as bool? ?? false
              : false,
        ),
      ),
      state,
    ),
    routes: [
      GoRoute(
        name: AppRouteName.carDetails,
        path: AppRouteName.carDetails,
        pageBuilder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;

          final car = extra['carData'] as Car;

          final isGuest = extra['isGuest'] as bool;

          return _pageBuilder(
            BlocProvider(
              create: (context) => sl<CarLoanBloc>(),
              child: CarDetailPage(
                car: car,
                isGuest: isGuest,
              ),
            ),
            state,
          );
        },
      ),
      GoRoute(
        name: AppRouteName.carLoanApplication,
        path: AppRouteName.carLoanApplication,
        pageBuilder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;
          final car = extra['carData'] as Car;
          return _pageBuilder(
            BlocProvider(
              create: (context) => sl<CarLoanBloc>(),
              child: CarLoanApplicationPage(
                car: car,
              ),
            ),
            state,
          );
        },
      ),
      GoRoute(
        name: AppRouteName.requiredDocumentsForCarLoan,
        path: AppRouteName.requiredDocumentsForCarLoan,
        pageBuilder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;
          return _pageBuilder(
            BlocProvider(
              create: (context) => sl<CarLoanBloc>(),
              child: RequiredDocumentsForCarLoan(
                bank: extra['bank'] as CarBankModel,
                selectedUpfrontPayment:
                    extra['selectedUpfrontPayment'] as String,
                carData: extra['carData'] as CarModel,
              ),
            ),
            state,
          );
        },
      ),
      GoRoute(
        name: AppRouteName.carLoanTermsConditions,
        path: 'terms-conditions',
        pageBuilder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;
          return _pageBuilder(
            BlocProvider(
              create: (context) => sl<CarLoanBloc>(),
              child: CarLoanTermsConditions(
                bank: extra['bank'] as CarBankModel,
                selectedUpfrontPayment:
                    extra['selectedUpfrontPayment'] as String,
                carData: extra['carData'] as CarModel,
              ),
            ),
            state,
          );
        },
      ),
    ],
  ),
];
