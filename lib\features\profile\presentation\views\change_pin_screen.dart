import 'dart:io';

import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/custom_text_input.dart';
import 'package:cbrs/core/common/widgets/custom_textfield.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/core/services/device/device_controller.dart';
import 'package:cbrs/core/services/injection_container.dart';
import 'package:cbrs/core/utils/form_validation.dart';
import 'package:cbrs/features/auth/data/datasources/auth_local_datasource.dart';
import 'package:cbrs/features/profile/application_layer/bloc/profile_bloc.dart';
import 'package:cbrs/features/profile/application_layer/bloc/profile_event.dart';
import 'package:cbrs/features/profile/data/models/pin_dto.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class ChangePinScreen extends StatefulWidget {
  const ChangePinScreen({super.key});

  @override
  State<ChangePinScreen> createState() => _ChangePinScreenState();
}

class _ChangePinScreenState extends State<ChangePinScreen> {
  final _formKey = GlobalKey<FormState>();

  final TextEditingController _currentPinController = TextEditingController();
  final TextEditingController _newPinController = TextEditingController();
  final TextEditingController _confirmPinController = TextEditingController();

  String deviceUUID = '';

  @override
  void initState() {
    // TODO: implement initState
    getDeviceUUID();
    super.initState();
  }

  @override
  void dispose() {
    _currentPinController.dispose();
    _newPinController.dispose();
    _confirmPinController.dispose();
    super.dispose();
  }

  bool isKeyboardVisible(BuildContext context) {
    return MediaQuery.of(context).viewInsets.bottom > 0;
  }

  Future<void> getDeviceUUID() async {
    final device = await sl<AuthLocalDataSource>().getDeviceUUID() ?? '';

    setState(() {
      deviceUUID = device;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        title: const Text(
          'Change PIN',
        ),
      ),
      body: BlocConsumer<ProfileBloc, ProfileState>(
        listener: (context, state) {
          if (state is PinUpdateError) {
            CustomToastification(context, message: state.message);
          } else if (state is PinUpdateLoad) {
            debugPrint('PIN IS LOADING ');
          } else if (state is PinUpdateCompleted) {
            CustomToastification(
              context,
              message: state.pinResponse.message,
              isError: !state.pinResponse.success,
            );

            debugPrint('PIN IS PinUpdateCompleted ');
            if (state.pinResponse.success) context.pop(true);

            // go back
          }
        },
        builder: (context, state) {
          return GestureDetector(
            onTap: () {
              debugPrint('Keyboard hide');
              FocusScope.of(context).unfocus();
            },
            child: SafeArea(
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Form(
                        key: _formKey,
                        child: Container(
                          padding: EdgeInsets.only(
                            top: 16.h,
                            left: 16.0.w,
                            right: 16.0.w,
                          ),
                        height: MediaQuery.sizeOf(context).height -
                            kToolbarHeight -
                            30,
                        child: SafeArea(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const CustomPageHeader(
                                pageTitle: 'Change PIN',
                                description:
                                    'Enter your current PIN and set a new one to enhance your security.',
                              ),
                              SizedBox(height: 20.h),
                              CustomTextInput(
                                hintText: 'Old PIN',
                                inputLabel: 'Old PIN',
                                isPassword: true,
                                controller: _currentPinController,
                                validator: (value) =>
                                    FormValidation.validatePin(
                                  value,
                                  'Old Pin',
                                ),
                                keyboardType: TextInputType.number,
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly,
                                  LengthLimitingTextInputFormatter(6),
                                ],
                              ),
                              SizedBox(height: 16.h),
                              CustomTextInput(
                                hintText: 'New PIN',
                                inputLabel: 'New PIN',
                                isPassword: true,
                                controller: _newPinController,
                                validator: (value) =>
                                    FormValidation.validatePin(
                                  value,
                                  'New Pin',
                                ),
                                keyboardType: TextInputType.number,
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly,
                                  LengthLimitingTextInputFormatter(6),
                                ],
                              ),
                              SizedBox(height: 16.h),
                              CustomTextInput(
                                hintText: 'Confirm New PIN',
                                inputLabel: 'Confirm New PIN',
                                isPassword: true,
                                controller: _confirmPinController,
                                validator: (value) =>
                                    FormValidation.validatePin(
                                  value,
                                  'Confirm Pin',
                                ),
                                keyboardType: TextInputType.number,
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly,
                                  LengthLimitingTextInputFormatter(6),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                Padding(
                  padding:
                      const EdgeInsets.only(left: 16, right: 16, bottom: 20),
                  child: CustomRoundedBtn(
                    btnText: 'Change PIN',
                    isLoading: state is PinUpdateLoad,
                    onTap: () {
                      if (_formKey.currentState!.validate()) _handleChangePin();
                    },
                  ),
                ),
              ],
            ),
          )
          );
        },
      ),
    );
  }

  Future<void> _handleChangePin() async {
    // Retrieve input values
    final currentPin = _currentPinController.text.trim();
    final newPin = _newPinController.text.trim();

    try {
      context.read<ProfileBloc>().add(
            UpdatePinEvent(
              PinModel(
                deviceUUID: deviceUUID,
                oldPin: currentPin,
                newPin: newPin,
                confirmPin: '',
              ),
            ),
          );

      final deviceController = Get.find<DeviceCheckController>();
      await deviceController.storePin(newPin);
    } catch (e) {
      CustomToastification(
        context,
        message: 'Error updating PIN for biometric login',
      );
    }
  }
}
