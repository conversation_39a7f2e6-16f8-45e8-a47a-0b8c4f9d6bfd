class MiniAppsConfig {
  static final List<String> allowedDomains = [
    'zmall.com',
    'teletv.et',
    'https://ethiopian-airline-connect-miniapp.vercel.app',
    'guzogo.com',
    'https://dstv-connect-miniapp.vercel.app',
    'adika.et',
    'muyalogy.com',
    'websprix.com',
    'hulubeje.com',
    'ride.et',
    'kuraztech.com',
    'ichereta.com',
    'queens-supermarket.et',
    'gelagle-market.et',
  ];

  static bool isAllowedDomain(String url) {
    try {
      final uri = Uri.parse(url);
      return allowedDomains.any((domain) => uri.host.endsWith(domain));
    } catch (e) {
      return false;
    }
  }
}

class MiniAppsUrls {
  // Mini Apps URLs
  static const String SchoolFeeUrl =
      'https://school-fee-connect-miniapp.vercel.app';
  static const String TrafficPenaltyUrl =
      'https://trafic-penality-connect-miniapp.vercel.app';
  static const String ethiopianAirlinesUrl =
      'https://ethiopian-airline-connect-miniapp.vercel.app';
  static const String guzoGoUrl = 'https://uat-dsa-guzogo-mini-app.vercel.app';
  static const String dstvUrl = 'https://dstv-connect-miniapp.vercel.app';
  static const String fuelPaymentUrl =
      'https://uat-dashen-fuel-mini-app.vercel.app';
  static const String teletvUrl = 'https://teletv-dashen-super-app.vercel.app/';
  static const String muyalogyUrl = 'https://muyalogy.com';
  static const String webSprixUrl = 'https://websprix.com';
  static const String huluBejeUrl = 'https://hulubeje.com';
  static const String rideUrl = 'https://ride.et';
  static const String kurazTechUrl = 'https://kuraztech.com';
  static const String icheretaUrl = 'https://ichereta.com';
  static const String zmallUrl = 'https://zmall.com';
  static const String adikaUrl = 'https://adika.et';

  // Shop Apps URLs
  static const String queensSupermarketUrl = 'https://queens-supermarket.et';
  static const String sasPharmacyUrl = 'https://sas-pharmacy.et';
  static const String gelagleMarketUrl = 'https://gelagle-official.com';
}
