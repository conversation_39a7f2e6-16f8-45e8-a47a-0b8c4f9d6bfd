import 'package:cbrs/core/res/media_res.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomNumberKeyboard extends StatelessWidget {
  const CustomNumberKeyboard({
    required this.onKeyPressed,
    super.key,
    this.useBackspace = true,
    this.textColor,
    this.fontSize,
    this.bottomPadding = 12,
  });
  final Function(String) onKeyPressed;
  final bool useBackspace;
  final Color? textColor;
  final double? fontSize;
  final double bottomPadding;

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    final cardWidth = screenWidth / 3 - 20;
    final cardHeight = MediaQuery.of(context).size.height * 0.07;
    return Container(
      padding: EdgeInsets.only(
        left: 12.w,
        right: 12.w,
        top: 12,
        bottom: bottomPadding,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24),
        color: const Color(0xFFF9F9F9),
      ),
      child: Wrap(
        spacing: 10,
        runSpacing: 10,
        children: [
          SizedBox(
            width: cardWidth,
            height: cardHeight,
            child: _buildKeys(context: context, keys: '1'),
          ),
          SizedBox(
            width: cardWidth,
            height: cardHeight,
            child: _buildKeys(
              context: context,
              keys: '2',
            ),
          ),
          SizedBox(
            width: cardWidth,
            height: cardHeight,
            child: _buildKeys(
              context: context,
              keys: '3',
            ),
          ),
          SizedBox(
            width: cardWidth,
            height: cardHeight,
            child: _buildKeys(
              context: context,
              keys: '4',
            ),
          ),
          SizedBox(
            width: cardWidth,
            height: cardHeight,
            child: _buildKeys(
              context: context,
              keys: '5',
            ),
          ),
          SizedBox(
            width: cardWidth,
            height: cardHeight,
            child: _buildKeys(
              context: context,
              keys: '6',
            ),
          ),
          SizedBox(
            width: cardWidth,
            height: cardHeight,
            child: _buildKeys(
              context: context,
              keys: '7',
            ),
          ),
          SizedBox(
            width: cardWidth,
            height: cardHeight,
            child: _buildKeys(
              context: context,
              keys: '8',
            ),
          ),
          SizedBox(
            width: cardWidth,
            height: cardHeight,
            child: _buildKeys(
              context: context,
              keys: '9',
            ),
          ),
          SizedBox(
            width: cardWidth,
            height: cardHeight,
            child: _buildKeys(
              context: context,
              keys: '.',
            ),
          ),
          SizedBox(
            width: cardWidth,
            height: cardHeight,
            child: _buildKeys(
              context: context,
              keys: '0',
            ),
          ),
          SizedBox(
            width: cardWidth,
            height: cardHeight,
            child: _buildKeys(
              context: context,
              keys: useBackspace ? '⌫' : 'clear',
            ),
          ),
        ],
      ),

      /*  
      
      : LayoutBuilder(
        builder: (context, constraints) {
          final buttonSize = constraints.maxWidth / 3;
          return GridView.builder(
            shrinkWrap: true,
            padding: EdgeInsets.zero,
            physics: const NeverScrollableScrollPhysics(),
            // padding:  EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: buttonSize / (buttonSize * 0.48),
              crossAxisSpacing: 8.w,
              mainAxisSpacing: 8.h,
            ),
            itemCount: 12,
            itemBuilder: (context, index) {
              final keys = [
                '1',
                '2',
                '3',
                '4',
                '5',
                '6',
                '7',
                '8',
                '9',
                '.',
                '0',
                if (useBackspace) '⌫' else 'clear',
              ];
              return Material(
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(32),
                ),
                child: InkWell(
                  onTap: () => onKeyPressed(keys[index]),
                  borderRadius: BorderRadius.circular(8),
                  child: Center(
                    child: keys[index] == '⌫' || keys[index] == 'clear'
                        ? Icon(
                            Icons.backspace_outlined,
                            color: Colors.black54,
                            size: fontSize ?? 20.sp,
                          )
                        : Text(
                            keys[index],
                            style: GoogleFonts.outfit(
                              fontSize: fontSize ?? 20.sp,
                              fontWeight: FontWeight.w500,
                              color: textColor ??
                                  (keys[index] == '⌫'
                                      ? Colors.black54
                                      : Colors.black87),
                            ),
                          ),
                  ),
                ),
              );
            },
          );
        },
      ),
 
 */
    );
  }

  Widget _buildKeys({
    required BuildContext context,
    required String keys,
  }) {
    return Material(
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(32),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(32),
        onTap: () {
          onKeyPressed(keys);
        },
        child: Center(
          child: keys == '⌫' || keys == 'clear'
              ? Icon(
                  Icons.backspace_outlined,
                  color: Colors.black54,
                  size: fontSize ?? 20.sp,
                )
              : Text(
                  keys,
                  style: GoogleFonts.outfit(
                    fontSize: 24.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
        ),
      ),
    );
  }
}
