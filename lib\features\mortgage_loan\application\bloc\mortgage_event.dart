import 'package:cbrs/features/mortgage_loan/domain/entities/mortgage_bank.dart';

abstract class MortgageBankEvent {}

class FetchMortgageBanksEvent extends MortgageBankEvent {
  FetchMortgageBanksEvent({
    required this.page,
    required this.productId, // Add this line, this.page = 1,, this.page =1,, this.page = 1,
    this.limit = 10,
  });
  final int page;
  final int limit;
  final String productId;
}

class SelectBankEvent extends MortgageBankEvent {
  SelectBankEvent(this.bank);
  final MortgageBank bank;
}

class FetchLoanTermsEvent extends MortgageBankEvent {
  FetchLoanTermsEvent({required this.bankId});
  final String bankId;
}

class SelectUpfrontPaymentEvent extends MortgageBankEvent {
  SelectUpfrontPaymentEvent(this.percentage);
  final String percentage;
}
