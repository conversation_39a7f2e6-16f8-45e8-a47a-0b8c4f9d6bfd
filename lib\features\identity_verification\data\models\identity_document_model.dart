import 'package:cbrs/core/utils/app_mapper.dart';
import 'package:cbrs/features/identity_verification/domain/entities/identity_document.dart';

class IdentityDocumentModel extends IdentityDocument {
  const IdentityDocumentModel({
    required super.id,
    required super.userId,
    required super.documentType,
    required super.frontImageUrl,
    required super.backImageUrl,
    required super.selfieImageUrl,
    required super.status,
    required super.uploadedAt,
    super.verifiedAt,
    super.rejectedAt,
    super.rejectionReason,
    super.metadata,
  });

  factory IdentityDocumentModel.fromJson(Map<String, dynamic> json) {
    // Parse status from string to enum
    final statusString = AppMapper.safeString(json['status']);
    final status = DocumentStatusExtension.fromString(statusString);

    // Parse dates
    final uploadedAtString = AppMapper.safeString(json['uploadedAt']);
    final uploadedAt = DateTime.tryParse(uploadedAtString) ?? DateTime.now();

    DateTime? verifiedAt;
    final verifiedAtString = AppMapper.safeString(json['verifiedAt']);
    if (verifiedAtString.isNotEmpty) {
      verifiedAt = DateTime.tryParse(verifiedAtString);
    }

    DateTime? rejectedAt;
    final rejectedAtString = AppMapper.safeString(json['rejectedAt']);
    if (rejectedAtString.isNotEmpty) {
      rejectedAt = DateTime.tryParse(rejectedAtString);
    }

    // Parse metadata
    Map<String, dynamic>? metadata;
    final metadataJson = json['metadata'];
    if (metadataJson is Map<String, dynamic>) {
      metadata = metadataJson;
    }

    return IdentityDocumentModel(
      id: AppMapper.safeString(json['_id'] ?? json['id']),
      userId: AppMapper.safeString(json['userId']),
      documentType: AppMapper.safeString(json['documentType']),
      frontImageUrl: AppMapper.safeString(json['frontImageUrl']),
      backImageUrl: AppMapper.safeString(json['backImageUrl']),
      selfieImageUrl: AppMapper.safeString(json['selfieImageUrl']),
      status: status,
      uploadedAt: uploadedAt,
      verifiedAt: verifiedAt,
      rejectedAt: rejectedAt,
      rejectionReason: AppMapper.safeString(json['rejectionReason']),
      metadata: metadata,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'userId': userId,
      'documentType': documentType,
      'frontImageUrl': frontImageUrl,
      'backImageUrl': backImageUrl,
      'selfieImageUrl': selfieImageUrl,
      'status': status.value,
      'uploadedAt': uploadedAt.toIso8601String(),
      'verifiedAt': verifiedAt?.toIso8601String(),
      'rejectedAt': rejectedAt?.toIso8601String(),
      'rejectionReason': rejectionReason,
      'metadata': metadata,
    };
  }

  @override
  IdentityDocumentModel copyWith({
    String? id,
    String? userId,
    String? documentType,
    String? frontImageUrl,
    String? backImageUrl,
    String? selfieImageUrl,
    DocumentStatus? status,
    DateTime? uploadedAt,
    DateTime? verifiedAt,
    DateTime? rejectedAt,
    String? rejectionReason,
    Map<String, dynamic>? metadata,
  }) {
    return IdentityDocumentModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      documentType: documentType ?? this.documentType,
      frontImageUrl: frontImageUrl ?? this.frontImageUrl,
      backImageUrl: backImageUrl ?? this.backImageUrl,
      selfieImageUrl: selfieImageUrl ?? this.selfieImageUrl,
      status: status ?? this.status,
      uploadedAt: uploadedAt ?? this.uploadedAt,
      verifiedAt: verifiedAt ?? this.verifiedAt,
      rejectedAt: rejectedAt ?? this.rejectedAt,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      metadata: metadata ?? this.metadata,
    );
  }

  // Factory constructor for creating from domain entity
  factory IdentityDocumentModel.fromEntity(IdentityDocument entity) {
    return IdentityDocumentModel(
      id: entity.id,
      userId: entity.userId,
      documentType: entity.documentType,
      frontImageUrl: entity.frontImageUrl,
      backImageUrl: entity.backImageUrl,
      selfieImageUrl: entity.selfieImageUrl,
      status: entity.status,
      uploadedAt: entity.uploadedAt,
      verifiedAt: entity.verifiedAt,
      rejectedAt: entity.rejectedAt,
      rejectionReason: entity.rejectionReason,
      metadata: entity.metadata,
    );
  }
}
