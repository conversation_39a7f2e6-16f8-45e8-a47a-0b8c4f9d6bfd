import 'dart:convert';
import 'dart:io';

import 'package:app_settings/app_settings.dart';
import 'package:cbrs/core/common/global_variable.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/services/device/session_timeout_service.dart';
import 'package:cbrs/core/services/firebase_notification_service/send_notification_service.dart';
import 'package:cbrs/core/services/routes/route.dart';
import 'package:cbrs/core/services/routes/route_name.dart';
import 'package:cbrs/features/notifications/presentation/widgets/notification_group.dart';
import 'package:cbrs/main.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_callkit_incoming/entities/entities.dart';
import 'package:flutter_callkit_incoming/flutter_callkit_incoming.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:permission_handler/permission_handler.dart';

class NotificationService {
  final FirebaseMessaging messaging = FirebaseMessaging.instance;

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  Future<void> initLocalNotifications() async {
    const androidInitSetting =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosInitSetting = DarwinInitializationSettings();
    const initializationSetting = InitializationSettings(
      android: androidInitSetting,
      iOS: iosInitSetting,
    );

    await _flutterLocalNotificationsPlugin.initialize(
      initializationSetting,
      onDidReceiveNotificationResponse: (NotificationResponse response) async {
        if (response.payload != null) {
          GlobalVariable.notificationData =
              jsonDecode(response.payload!) as Map<String, dynamic>;
          if (mainNavigatorKey.currentContext != null) {
            handleNotificationRedirect(mainNavigatorKey.currentContext!,);
          }
        }
      },
    );
  }

  Future<void> setUpFirebaseMessaging(BuildContext context) async {
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) async {
      await handleMessage(message);

      if (!Get.find<SessionTimeoutService>().isSessionTimeout.value) {
        handleNotificationRedirect(context);
      }
    });

    final initialMessage = await messaging.getInitialMessage();
    if (initialMessage != null) {
      await handleMessage(initialMessage);

      if (!Get.find<SessionTimeoutService>().isSessionTimeout.value) {
        handleNotificationRedirect(context);
      }
    }

    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      if (message.data['notificationType'] == 'VIDEO_CALL') {
        displayIncomingCall(message.data);
        return;
      }

      if (message.data['notificationType'] == 'VIDEO_CALL_REJECTED') {
        if (context.mounted) context.pop();
        return;
      }

      if (Platform.isIOS) {
        iosForegroundMessage(message);
      }
      if (Platform.isAndroid) {
        showNotification(message);
      }
    });
  }

  Future<void> handleMessage(RemoteMessage message) async {
    GlobalVariable.isAppOpenFromNotification = true;
    GlobalVariable.notificationData = message.data;
  }

  Future<void> showNotification(RemoteMessage message) async {
    final androidChannel = AndroidNotificationChannel(
      message.notification?.android?.channelId ?? 'default_channel',
      message.notification?.android?.channelId ?? 'Default Channel',
      importance: Importance.high,
    );

    final androidNotificationDetails = AndroidNotificationDetails(
      androidChannel.id,
      androidChannel.name,
      channelDescription: 'Channel Description',
      importance: Importance.high,
      priority: Priority.high,
      sound: androidChannel.sound,
    );

    const iosNotificationDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    final notificationDetails = NotificationDetails(
      android: androidNotificationDetails,
      iOS: iosNotificationDetails,
    );

    await _flutterLocalNotificationsPlugin.show(
      0,
      message.notification?.title,
      message.notification?.body,
      notificationDetails,
      payload: jsonEncode(message.data),
    );
  }

  Future<void> iosForegroundMessage(RemoteMessage message) async {
    await messaging.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );
    debugPrint('iOS foreground message: $message');
  }

  Future<void> requestNotificationPermission() async {
    final settings = await messaging.requestPermission(
      provisional: true,
    );

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      debugPrint('User granted notification permission');
    } else if (settings.authorizationStatus ==
        AuthorizationStatus.provisional) {
      debugPrint('User granted provisional notification permission');
    } else {
      _showPermissionDialog();
    }
  }

  void _showPermissionDialog() {
    showDialog<void>(
      context: mainNavigatorKey.currentContext!,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Notification Permission Required'),
          content: const Text(
              'This app needs notification permissions to function properly.'
              ' Please enable it in the app settings.'),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () => Navigator.of(context).pop(),
            ),
            TextButton(
              child: const Text('Open Settings'),
              onPressed: () {
                Navigator.of(context).pop();
                AppSettings.openAppSettings(type: AppSettingsType.notification);
              },
            ),
          ],
        );
      },
    );
  }
}

void handleNotificationRedirect(BuildContext context) {
  GlobalVariable.isAppOpenFromNotification = false;
  final notificationData = GlobalVariable.notificationData;

  if (notificationData.containsKey('notificationType') &&
      notificationData['notificationType'] == 'TRANSACTION') {
    context.pushNamed(
      AppRouteName.notificationDetails,
      extra: NotificationItem(
        title: notificationData['title'] as String? ?? '',
        description: notificationData['description'] as String? ?? '',
        isRead: false,
        id: notificationData['id'] as String? ?? '',
        date: DateTime.now().toString(),
        transactionType: notificationData['transactionType'] as String? ?? '',
      ),
    );
  }

  if (notificationData.containsKey('notificationType') &&
      notificationData['notificationType'] == 'CHAT') {
    context.pushNamed(AppRouteName.chat);
  }

  GlobalVariable.notificationData = {};
}

Future<void> displayIncomingCall(Map<String, dynamic> data) async {
  final params = CallKitParams(
    id: data['chatRoomId'] as String?,
    nameCaller: '${data['receiverFirstName']} ${data['receiverLastName']}',
    appName: 'CONNECT',
    avatar: data['receiverAvatar'] as String?,
    handle: 'Incoming call',
    type: 0,
    duration: 30000,
    textAccept: 'Accept',
    textDecline: 'Decline',
    extra: {
      'chatRoomId': data['chatRoomId'],
      'receiverId': data['receiverId'],
      'senderId': data['senderId'],
      'receiverAvatar': data['receiverAvatar'],
      'receiverFirstName': data['receiverFirstName'],
      'receiverLastName': data['receiverLastName'],
      'senderLastName': data['senderLastName'],
      'senderFirstName': data['senderFirstName'],
      'senderFCMToken': data['senderFCMToken'],
      'isIncoming': true,
    },
    missedCallNotification: NotificationParams(
      showNotification: false,
      subtitle: 'Missed call from ${data['receiverFirstName']} '
          '${data['receiverLastName']}',
      callbackText: 'Call back',
      isShowCallback: true,
      count: 1,
    ),
    android: const AndroidParams(
      isCustomNotification: false,
      isShowLogo: true,
      ringtonePath: 'system_ringtone_default',
      backgroundColor: '#0955fa',
      backgroundUrl: MediaRes.logo,
      actionColor: '#4CAF50',
      textColor: '#ffffff',
      isImportant: true,
      isShowFullLockedScreen: true,
    ),
    ios: const IOSParams(
      iconName: 'CallKitLogo',
      handleType: '',
      supportsVideo: true,
      maximumCallGroups: 2,
      maximumCallsPerCallGroup: 1,
      audioSessionMode: 'default',
      audioSessionActive: true,
      audioSessionPreferredSampleRate: 44100,
      audioSessionPreferredIOBufferDuration: 0.005,
      supportsDTMF: true,
      supportsHolding: true,
      supportsGrouping: false,
      supportsUngrouping: false,
      ringtonePath: 'system_ringtone_default',
    ),
  );

  await FlutterCallkitIncoming.showCallkitIncoming(params);
}

void initCallListener() {
  WidgetsBinding.instance.addPostFrameCallback((_) async {
    final initialCall = await FlutterCallkitIncoming.activeCalls();
    if (initialCall != null) {
      if (initialCall is List &&
          initialCall.length > 0 &&
          initialCall[0]['accepted'] == true) {
        await FlutterCallkitIncoming.endAllCalls();
        final data = initialCall[0]['extra'];
        await router.pushNamed(
          AppRouteName.videoCall,
          extra: {
            'chatRoomId': data['chatRoomId'],
            'senderId': data['senderId'],
            'receiverAvatar': data['receiverAvatar'],
            'receiverFirstName': data['receiverFirstName'],
            'receiverLastName': data['receiverLastName'],
            'receiverId': data['receiverId'],
            'isIncoming': true,
          },
        );
      }
    }
  });

  FlutterCallkitIncoming.onEvent.listen((event) async {
    switch (event?.event) {
      case Event.actionCallAccept:
        final data = event?.body;
        await router.pushNamed(
          AppRouteName.videoCall,
          extra: {
            'chatRoomId': data?['extra']['chatRoomId'],
            'senderId': data?['extra']['senderId'],
            'receiverAvatar': data?['extra']['receiverAvatar'],
            'receiverFirstName': data?['extra']['receiverFirstName'],
            'receiverLastName': data?['extra']['receiverLastName'],
            'receiverId': data?['extra']['receiverId'],
            'isIncoming': true,
          },
        );
      case Event.actionCallDecline:
        final data = event?.body;
        await FlutterCallkitIncoming.endAllCalls();
        await SendNotificationService().sendVideoCallRejectedNotification(
          fcmToken: data?['extra']['senderFCMToken'] as String? ?? '',
        );
      case null:
      case Event.actionCallIncoming:
      case Event.actionCallStart:
      case Event.actionCallEnded:
      case Event.actionCallCallback:
      case Event.actionDidUpdateDevicePushTokenVoip:
      case Event.actionCallTimeout:
      case Event.actionCallToggleHold:
      case Event.actionCallToggleMute:
      case Event.actionCallToggleDmtf:
      case Event.actionCallToggleGroup:
      case Event.actionCallToggleAudioSession:
      case Event.actionCallCustom:
    }
  });
}
