import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

enum IdentityVerificationStep {
  instructions,
  faceCapture,
  faceReview,
  documentCapture,
  documentReview,
}

class IdentityVerificationStatusIndicator extends StatelessWidget {
  const IdentityVerificationStatusIndicator({
    super.key,
    required this.currentStep,
    this.showBackButton = true,
    this.onBackPressed,
  });

  final IdentityVerificationStep currentStep;
  final bool showBackButton;
  final VoidCallback? onBackPressed;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 56.h, // Fixed height for consistent layout
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
      child: Stack(
        children: [
          // Progress indicators - perfectly centered
          Center(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // First indicator (instructions)
                _buildIndicator(0),
                SizedBox(width: 8.w),

                // Second indicator (face capture)
                _buildIndicator(1),
                SizedBox(width: 8.w),

                // Third indicator (face review)
                _buildIndicator(2),
                SizedBox(width: 8.w),

                // Fourth indicator (document capture)
                _buildIndicator(3),
                <PERSON><PERSON><PERSON>ox(width: 8.w),

                // Fifth indicator (document review)
                _buildIndicator(4),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIndicator(int index) {
    final isCompleted = _isStepCompleted(index);
    final isCurrent = _isStepCurrent(index);

    return Container(
      width: 60.w,
      height: 4.h,
      decoration: BoxDecoration(
        color: isCompleted || isCurrent
            ? _getIndicatorColor()
            : _getInactiveIndicatorColor(),
        borderRadius: BorderRadius.circular(2.r),
      ),
    );
  }

  bool _isStepCompleted(int index) {
    switch (currentStep) {
      case IdentityVerificationStep.instructions:
        return false; // No steps completed yet
      case IdentityVerificationStep.faceCapture:
        return index == 0; // Only first step (instructions) completed
      case IdentityVerificationStep.faceReview:
        return index <= 1; // First two steps completed
      case IdentityVerificationStep.documentCapture:
        return index <= 2; // First three steps completed
      case IdentityVerificationStep.documentReview:
        return index <= 3; // First four steps completed
    }
  }

  bool _isStepCurrent(int index) {
    switch (currentStep) {
      case IdentityVerificationStep.instructions:
        return index == 0; // Currently on instructions (step 1)
      case IdentityVerificationStep.faceCapture:
        return index == 1; // Currently on face capture (step 2)
      case IdentityVerificationStep.faceReview:
        return index == 2; // Currently on face review (step 3)
      case IdentityVerificationStep.documentCapture:
        return index == 3; // Currently on document capture (step 4)
      case IdentityVerificationStep.documentReview:
        return index == 4; // Currently on document review (step 5)
    }
  }

  Color _getIndicatorColor() {
    switch (currentStep) {
      case IdentityVerificationStep.instructions:
        return Colors.black; // Black indicators on light background
      case IdentityVerificationStep.faceCapture:
      case IdentityVerificationStep.documentCapture:
        return Colors.white; // White indicators on dark background
      case IdentityVerificationStep.faceReview:
      case IdentityVerificationStep.documentReview:
        return Colors.black; // Black indicators on light background
    }
  }

  Color _getInactiveIndicatorColor() {
    switch (currentStep) {
      case IdentityVerificationStep.instructions:
        return Colors.grey[400]!; // Light grey on light background
      case IdentityVerificationStep.faceCapture:
      case IdentityVerificationStep.documentCapture:
        return Colors.grey[600]!; // Darker grey on dark background
      case IdentityVerificationStep.faceReview:
      case IdentityVerificationStep.documentReview:
        return Colors.grey[400]!; // Light grey on light background
    }
  }

  Color _getBackButtonColor() {
    switch (currentStep) {
      case IdentityVerificationStep.instructions:
        return Colors.black;
      case IdentityVerificationStep.faceCapture:
      case IdentityVerificationStep.documentCapture:
        return Colors.white;
      case IdentityVerificationStep.faceReview:
      case IdentityVerificationStep.documentReview:
        return Colors.black;
    }
  }
}
