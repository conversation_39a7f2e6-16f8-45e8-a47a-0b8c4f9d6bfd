import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_button.dart';
import 'package:cbrs/core/common/widgets/custom_cached_image.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/features/add_money/data/models/linked_account_response_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cbrs/features/add_money/application/bloc/add_money_bloc.dart';
import 'package:cbrs/features/add_money/domain/entities/linked_account.dart';
import 'package:shimmer/shimmer.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:cached_network_image/cached_network_image.dart';

class AccountSelector extends StatefulWidget {
  const AccountSelector({
    required this.onAccountSelected,
    super.key,
    this.selectedAccount,
  });
  final Function(LinkedAccount) onAccountSelected;
  final LinkedAccount? selectedAccount;

  @override
  State<AccountSelector> createState() => _AccountSelectorState();
}

class _AccountSelectorState extends State<AccountSelector> {
  LinkedAccount? _localSelectedAccount;
  LinkedAccount? _tempSelectedAccount;
  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _localSelectedAccount = widget.selectedAccount;
    _tempSelectedAccount = widget.selectedAccount;

    _scrollController.addListener(_onScroll);

    // Initial load
    context.read<AddMoneyBloc>().add(
          const GetLinkedAccountsEvent(
            page: 1,
            limit: 10,
          ),
        );
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      final state = context.read<AddMoneyBloc>().state;
      if (state is LinkedAccountsLoaded &&
          !_isLoadingMore &&
          state.accounts.hasNextPage) {
        setState(() => _isLoadingMore = true);

        context.read<AddMoneyBloc>().add(
              GetLinkedAccountsEvent(
                page: state.accounts.page + 1,
                limit: 10,
              ),
            );
      }
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AddMoneyBloc, AddMoneyState>(
      listener: (context, state) {
        if (state is AddMoneyError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        }
      },
      builder: (context, state) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(24.r),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: 16.w,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 12.h,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: 35,
                          height: 2.5,
                          decoration: BoxDecoration(
                            color: Colors.black,
                            borderRadius: BorderRadius.circular(3),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 12.h,
                    ),
                    Text(
                      'Select Linked Account',
                      style: GoogleFonts.outfit(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      'Select a linked account to top up your wallet.',
                      style: GoogleFonts.outfit(
                        fontSize: 12.sp,
                        color: Colors.black.withOpacity(0.4),
                      ),
                    ),
                    SizedBox(height: 24.h),
                  ],
                ),
              ),
              if (state is AddMoneyLoading)
                const AccountSelectorShimmer()
              else if (state is LinkedAccountsLoaded)
                Flexible(
                  child: Scrollbar(
                    radius: const Radius.circular(10),
                    thickness: 6,
                    thumbVisibility: true,
                    child: ListView.separated(
                      padding: EdgeInsets.only(
                        left: 16.w,
                        right: 16.w,
                      ),
                      itemCount: state.accounts.docs.length,
                      separatorBuilder: (context, index) =>
                          SizedBox(height: 8.h),
                      itemBuilder: (context, index) {
                        final entityAccount = state.accounts.docs[index];

                        final account = entityAccount;
                        final isSelected =
                            _tempSelectedAccount?.id == account.id;

                        return Container(
                          decoration: BoxDecoration(
                            color: const Color(0xFFF8F8F8),
                            borderRadius: BorderRadius.circular(16.r),
                          ),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(16.r),
                              onTap: () {
                                setState(() {
                                  _tempSelectedAccount = account;
                                });
                              },
                              child: Padding(
                                padding: EdgeInsets.all(16.w),
                                child: Row(
                                  children: [
                                    Container(
                                      width: 24.w,
                                      height: 24.w,
                                      decoration: BoxDecoration(
                                        color: isSelected
                                            ? Theme.of(context).primaryColor
                                            : Colors.transparent,
                                        border: !isSelected
                                            ? Border.all(
                                                color: Theme.of(context)
                                                    .primaryColor,
                                                width: 2,
                                              )
                                            : null,
                                        shape: BoxShape.circle,
                                      ),
                                      child: isSelected
                                          ? Icon(
                                              Icons.check,
                                              size: 16.w,
                                              color: Colors.white,
                                              weight: 8,
                                            )
                                          : null,
                                    ),
                                    SizedBox(width: 12.w),
                                    SizedBox(
                                      width: 40.w,
                                      height: 40.w,
                                      child: CustomCachedImage(
                                        url: account.bank.logo,
                                      ),

                                      // CachedNetworkImage(
                                      //   imageUrl: account.bank.logo,
                                      //   fit: BoxFit.contain,
                                      //   placeholder: (context, url) =>
                                      //       Container(
                                      //     decoration: BoxDecoration(
                                      //       color: Colors.grey[100],
                                      //       borderRadius:
                                      //           BorderRadius.circular(8.r),
                                      //     ),
                                      //     child: const Center(
                                      //       child: CircularProgressIndicator(
                                      //         strokeWidth: 2,
                                      //         color: Color(0xFF065234),
                                      //       ),
                                      //     ),
                                      //   ),
                                      //   errorWidget: (context, url, error) =>
                                      //       Container(
                                      //     decoration: BoxDecoration(
                                      //       color: Colors.grey[100],
                                      //       borderRadius:
                                      //           BorderRadius.circular(8.r),
                                      //     ),
                                      //     child: Icon(
                                      //       Icons.account_balance,
                                      //       size: 24.w,
                                      //       color: Colors.grey[400],
                                      //     ),
                                      //   ),
                                      // ),
                                    ),
                                    SizedBox(width: 12.w),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          CustomBuildText(
                                            text:
                                                '${account.member.firstName} ${account.member.middleName} ${account.member.lastName}',
                                            style: GoogleFonts.outfit(
                                              fontSize: 14.sp,
                                              fontWeight: FontWeight.w400,
                                            ),
                                          ),
                                          SizedBox(height: 6.h),
                                          Text(
                                            account.accountNumber,
                                            style: GoogleFonts.outfit(
                                              fontSize: 12.sp,
                                              color: Colors.grey[600],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              Padding(
                padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 23.h),
                child: CustomRoundedBtn(
                  btnText: 'Continue',
                  isLoading: false,
                  onTap: _tempSelectedAccount != null
                      ? () {
                          _localSelectedAccount = _tempSelectedAccount;
                          widget.onAccountSelected(_tempSelectedAccount!);
                          Navigator.pop(context);
                        }
                      : null,
                ),
              ),
              // Padding(
              //   padding: EdgeInsets.all(16.w),
              //   child: SizedBox(
              //     width: double.infinity,
              //     child: ElevatedButton(
              //       style: ElevatedButton.styleFrom(
              //         backgroundColor: const Color(0xFF065234),
              //         padding: EdgeInsets.symmetric(vertical: 16.h),
              //         shape: RoundedRectangleBorder(
              //           borderRadius: BorderRadius.circular(32.r),
              //         ),
              //         elevation: 0,
              //       ),
              //       child: Text(
              //         'Continue',
              //         style: GoogleFonts.outfit(
              //           fontSize: 16.sp,
              //           fontWeight: FontWeight.w600,
              //           color: Colors.white,
              //         ),
              //       ),
              //     ),
              //   ),
              // ),
            ],
          ),
        );
      },
    );
  }
}

class AccountSelectorShimmer extends StatelessWidget {
  const AccountSelectorShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Flexible(
      child: ListView.separated(
        padding: EdgeInsets.only(
          left: 16.w,
          right: 24.w,
        ),
        itemCount: 5,
        separatorBuilder: (context, index) => SizedBox(height: 12.h),
        itemBuilder: (context, index) {
          return Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.03),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Shimmer.fromColors(
              baseColor: const Color(0xFFE8ECF4),
              highlightColor: const Color(0xFFF5F7FA),
              child: Padding(
                padding: EdgeInsets.all(16.w),
                child: Row(
                  children: [
                    // Radio button shimmer
                    Container(
                      width: 24.w,
                      height: 24.w,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.grey[200]!,
                          width: 2,
                        ),
                      ),
                    ),
                    SizedBox(width: 12.w),
                    // Bank logo shimmer
                    Container(
                      width: 40.w,
                      height: 40.w,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8.r),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.04),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            width: (140 + (index * 15)).w,
                            height: 16.h,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(4.r),
                            ),
                          ),
                          SizedBox(height: 8.h),
                          Container(
                            width: (90 + (index * 10)).w,
                            height: 14.h,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(4.r),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
