import 'package:cbrs/core/extensions/context_extensions.dart';
import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/core/utils/get_app_theme_controller.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_bloc.dart';
import 'package:cbrs/features/home/<USER>/bloc/home_state.dart';
import 'package:cbrs/features/home/<USER>/bloc/wallet_balance_bloc.dart';
import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:flutter/material.dart';
import 'package:get/instance_manager.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cbrs/core/common/global_variable.dart';

class MainBottomNavBar extends StatefulWidget {
  const MainBottomNavBar({
    required this.currentIndex,
    required this.onTap,
    required this.onDoubleTap,
    super.key,
  });
  final int currentIndex;
  final Function(int) onTap;
  final Function(int) onDoubleTap;

  @override
  State<MainBottomNavBar> createState() => _MainBottomNavBarState();
}

class _MainBottomNavBarState extends State<MainBottomNavBar> {
  // Add a helper method for handling tab changes directly
  void _handleTabChange(int index) {
    GlobalVariable.currentTabIndex = index;
    widget.onTap(index);
  }

  bool isDollarAccount = true;

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<WalletBalanceBloc, HomeState>(
      listener: (context, state) {
        if (state is WalletLoadedState) {
          setState(() {
            isDollarAccount = state.isUsdWallet;
          });
        }
      },

      // selector: (state) => state is WalletLoadedState && state.isUsdWallet,
      builder: (context, state) {
        return Container(
          decoration: BoxDecoration(
            color: context.theme.cardColor,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: Container(
            height: 88.h,
            padding: EdgeInsets.symmetric(horizontal: 8.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildNavItem(
                  context,
                  HugeIcons.strokeRoundedHome02,
                  HugeIcons.strokeRoundedHome02,
                  'Home',
                  0,
                  hasCustomIcon: true,
                  customActiveIconLocation: MediaRes.activeHome,
                  customInActiveIconLocation: MediaRes.inactiveHome,
                ),
                _buildNavItem(
                  context,
                  isDollarAccount
                      ? HugeIcons.strokeRoundedPlaySquare
                      : HugeIcons.strokeRoundedAppStore,
                  isDollarAccount
                      ? HugeIcons.strokeRoundedPlaySquare
                      : HugeIcons.strokeRoundedAppStore,
                  isDollarAccount ? 'My Loans' : 'Mini Apps',
                  1,
                  hasCustomIcon: true,
                  customActiveIconLocation: isDollarAccount
                      ? MediaRes.activeLoanIcon
                      : MediaRes.activeMiniApps,
                  customInActiveIconLocation: isDollarAccount
                      ? MediaRes.inactiveLoanIcon
                      : MediaRes.inactiveMiniApps,
                ),
                _buildNavItem(
                  context,
                  HugeIcons.strokeRoundedBubbleChat,
                  HugeIcons.strokeRoundedBubbleChat,
                  'Chat',
                  2,
                  hasCustomIcon: true,
                  customActiveIconLocation: MediaRes.activeChat,
                  customInActiveIconLocation: MediaRes.inactiveChat,
                ),
                _buildNavItem(
                  context,
                  FluentIcons.history_24_regular,
                  FluentIcons.history_24_regular,
                  'Transactions',
                  3,
                  hasCustomIcon: true,
                  customActiveIconLocation: MediaRes.activeTransaction,
                  customInActiveIconLocation: MediaRes.inactiveTransactionc,
                ),
                _buildNavItem(
                  context,
                  FluentIcons.person_24_regular,
                  FluentIcons.person_24_regular,
                  'Profile',
                  4,
                  hasCustomIcon: true,
                  customActiveIconLocation: MediaRes.activePerson,
                  customInActiveIconLocation: MediaRes.inactivePerson,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildNavItem(
    BuildContext context,
    IconData icon,
    IconData activeIcon,
    String label,
    int index, {
    bool hasCustomIcon = false,
    String customActiveIconLocation = '',
    String customInActiveIconLocation = '',
  }) {
    final isSelected = widget.currentIndex == index;
    final themeController = Get.find<GetAppThemeController>();

    return Expanded(
      child: GestureDetector(
        onTap: () => _handleTabChange(index),
        onDoubleTap: () => widget.onDoubleTap(index),
        behavior: HitTestBehavior.opaque,
        child: Container(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 200),
                child: hasCustomIcon
                    ? ShaderMask(
                        shaderCallback: (Rect bounds) {
                          if (!isSelected) {
                            return LightModeTheme()
                                .grayGradient
                                .createShader(bounds);
                          }
                          return isDollarAccount
                              ? LightModeTheme()
                                  .usdGradient
                                  .createShader(bounds)
                              : LightModeTheme()
                                  .primaryGradient
                                  .createShader(bounds);
                        },
                        child: Image.asset(
                          width: 24.w,
                          height: 24.h,
                          isSelected
                              ? customActiveIconLocation
                              : customInActiveIconLocation,
                          color: Colors.white,
                          key: ValueKey(isSelected),
                        ),
                      )
                    : ShaderMask(
                        shaderCallback: (Rect bounds) {
                          if (!isSelected) {
                            return LightModeTheme()
                                .grayGradient
                                .createShader(bounds);
                          }
                          return isDollarAccount
                              ? LightModeTheme()
                                  .usdGradient
                                  .createShader(bounds)
                              : LightModeTheme()
                                  .primaryGradient
                                  .createShader(bounds);
                        },
                        child: Icon(
                          isSelected ? activeIcon : icon,
                          size: 24,
                          color: Colors.white,
                          key: ValueKey(isSelected),
                        ),
                      ),
              ),
              const SizedBox(height: 4),
              AnimatedDefaultTextStyle(
                duration: const Duration(milliseconds: 200),
                style: GoogleFonts.outfit(
                  fontSize: 12.sp,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  color: isSelected
                      ? isDollarAccount
                          ? LightModeTheme().primaryColorUSD
                          : LightModeTheme().primaryColor
                      : const Color(0xFFCACACA),
                ),
                child: Text(label),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
