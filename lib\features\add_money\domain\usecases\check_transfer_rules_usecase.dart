import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/send_money/data/models/check_transfer_rules_response.dart';
import 'package:cbrs/features/add_money/domain/repositories/add_money_repository.dart';
import 'package:equatable/equatable.dart';

class CheckTransferRules extends UsecaseWithParams<CheckTransferRulesResponse,
    CheckTransferRulesParams> {

  const CheckTransferRules(this._repository);
  final AddMoneyRepository _repository;

  @override
  ResultFuture<CheckTransferRulesResponse> call(
      CheckTransferRulesParams params,) async {
    return _repository.checkTransferRules(
      amount: params.amount,
      currency: params.currency,
      accountNumber: params.accountNumber,
      bankId: params.bankId
    );
  }
}

class CheckTransferRulesParams extends Equatable {

  const CheckTransferRulesParams({
    required this.amount,
    required this.currency,
    required this.bankId,
    required this.accountNumber
  });
  final double amount;
  final String currency;
  final String bankId;
  final String accountNumber;

  @override
  List<Object?> get props => [amount, currency];
}
