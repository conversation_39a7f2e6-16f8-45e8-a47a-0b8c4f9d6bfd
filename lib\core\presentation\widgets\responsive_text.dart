import 'package:cbrs/core/utils/text_scaling.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class ResponsiveText extends StatelessWidget {
  final String text;
  final double fontSize;
  final FontWeight? fontWeight;
  final TextAlign? textAlign;
  final Color? color;
  final String? fontFamily;
  final TextOverflow? overflow;
  final int? maxLines;

  const ResponsiveText({
    super.key,
    required this.text,
    required this.fontSize,
    this.fontWeight,
    this.textAlign,
    this.color,
    this.fontFamily,
    this.overflow,
    this.maxLines,
  });

  @override
  Widget build(BuildContext context) {
    final responsiveFontSize = TextScaling.getResponsiveSize(context, fontSize);

    return Text(
      text,
      style: fontFamily == 'outfit' 
          ? GoogleFonts.outfit(
              fontSize: responsiveFontSize,
              fontWeight: fontWeight,
              color: color,
            )
          : TextStyle(
              fontSize: responsiveFontSize,
              fontWeight: fontWeight,
              color: color,
            ),
      textAlign: textAlign,
      overflow: overflow,
      maxLines: maxLines,
    );
  }
}