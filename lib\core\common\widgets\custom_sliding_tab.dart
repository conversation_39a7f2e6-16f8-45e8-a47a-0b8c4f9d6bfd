import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/res/cbrs_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

typedef CustomSlidingFunction = void Function(int index);

class CustomSlidingTab extends StatefulWidget {
  const CustomSlidingTab({
    required this.onTabChanged,
    required this.tabs,
    this.showShadow = false,
    super.key,
  }) : assert(tabs.length > 1, 'Tabs must contain more than 1 items.');

  final List<String> tabs;
  final bool showShadow;
  final CustomSlidingFunction onTabChanged;

  @override
  State<CustomSlidingTab> createState() => _CustomSlidingTabState();
}

class _CustomSlidingTabState extends State<CustomSlidingTab> {
  int index = 0;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      padding: const EdgeInsets.all(6),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F8F8),
        borderRadius: BorderRadius.circular(12),
        boxShadow: widget.showShadow
            ? [
                BoxShadow(
                  color: const Color(0xFF000000).withOpacity(0.14),
                  blurRadius: 24,
                ),
              ]
            : null,
      ),
      child: Stack(
        fit: StackFit.expand,
        children: [
          ConstrainedBox(
            constraints: const BoxConstraints(
              minHeight: 50,
            ),
            child: AnimatedContainer(
              margin: index == 0
                  ? EdgeInsets.only(
                      right: (MediaQuery.sizeOf(
                                context,
                              ).width /
                              2) -
                          20,
                    )
                  : EdgeInsets.only(
                      left: (MediaQuery.sizeOf(
                                context,
                              ).width /
                              2) -
                          20,
                    ),
              duration: const Duration(
                milliseconds: 350,
              ),
              curve: Curves.fastOutSlowIn,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          Row(
            children: [
              Flexible(
                flex: 5,
                fit: FlexFit.tight,
                child: InkWell(
                  onTap: () {
                    setState(() {
                      index = 0;
                    });

                    widget.onTabChanged.call(index);
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 10,
                      horizontal: 10,
                    ),
                    child: CustomBuildText(
                      text: widget.tabs[0],
                      fontWeight: FontWeight.w600,
                      fontSize: 16.sp,
                      color: index == 0
                          ? Colors.white
                          : Theme.of(context).primaryColor,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),
              Flexible(
                flex: 5,
                fit: FlexFit.tight,
                child: InkWell(
                  onTap: () {
                    setState(() {
                      index = 1;
                    });

                    widget.onTabChanged.call(index);
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 10,
                      horizontal: 20,
                    ),
                    child: CustomBuildText(
                      text: widget.tabs[1],
                      fontWeight: FontWeight.w600,
                      fontSize: 16.sp,
                      color: index == 1
                          ? Colors.white
                          : Theme.of(context).primaryColor,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
