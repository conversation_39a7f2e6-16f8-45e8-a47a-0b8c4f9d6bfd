// import 'package:appoint_dr/core/widgets/custom_button_card.dart';
// import 'package:appoint_dr/features/auth/presentations/views/register_screen.dart';
// import 'package:appoint_dr/features/auth/presentations/widget/auth_page_header.dart';
// import 'package:flutter/material.dart';
// import 'package:pinput/pinput.dart';

// import 'package:sms_autofill/sms_autofill.dart';

// final defaultPinTheme = PinTheme(
//   width: 56,
//   height: 56,

//   textStyle: TextStyle(
//     fontSize: 20,
//     color: Color.fromRGBO(30, 60, 87, 1),
//     fontWeight: FontWeight.w600,
//   ),
//   decoration: BoxDecoration(
//     border: Border.all(color: Color.fromRGBO(234, 239, 243, 1)),
//     borderRadius: BorderRadius.circular(20),
//   ),
// );

// final focusedPinTheme = defaultPinTheme.copyDecorationWith(
//   border: Border.all(color: Color.fromRGBO(114, 178, 238, 1)),
//   borderRadius: BorderRadius.circular(8),
// );

// final submittedPinTheme = defaultPinTheme.copyWith(
//   decoration: defaultPinTheme.decoration?.copyWith(
//     color: Color.fromRGBO(234, 239, 243, 1),
//   ),
// );

// class OtpConfirmScreen extends StatefulWidget {
//   const OtpConfirmScreen({super.key});

//   @override
//   State<OtpConfirmScreen> createState() => _OtpConfirmScreenState();
// }

// class _OtpConfirmScreenState extends State<OtpConfirmScreen> {
//   late String _enteredPin;

//   @override
//   void initState() {
//     super.initState();
//     _initSmsRetriever();
//   }

//   // Initialize SMS Retriever
//   Future<void> _initSmsRetriever() async {
//     SmsAutoFill().listenForCode;
//   }

//   void _navigateToRegisterScreen(BuildContext context) {
//     debugPrint('Navigating to RegisterScreen');
//     Navigator.pushReplacement(
//       context,
//       MaterialPageRoute(builder: (context) => RegisterScreen()),
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(title: Text("Send OTP Code")),

//       body: Container(
//         padding: EdgeInsets.symmetric(horizontal: 16),
//         child: Column(
//           children: [
//             Expanded(
//               child: SingleChildScrollView(
//                 child: Column(
//                   children: [
//                     AuthPageHeader(
//                       title: "Register with Phone number",
//                       description:
//                           'Please enter your number to continue your registration',
//                     ),

//                     SizedBox(height: 10),
//                     Pinput(
//                       length: 6,
//                       defaultPinTheme: defaultPinTheme,
//                       focusedPinTheme: focusedPinTheme,
//                       submittedPinTheme: submittedPinTheme,
//                       validator: (s) {
//                         return s == '2222' ? null : 'Pin is incorrect';
//                       },
//                       // smsRetriever: SmsRetriever.,
//                       pinputAutovalidateMode: PinputAutovalidateMode.onSubmit,
//                       showCursor: true,
//                       onCompleted: (pinCode) {
//                         setState(() {
//                           _enteredPin = pinCode;
//                         });
//                         print("Pin entered: $_enteredPin");
//                       },
//                     ),
//                   ],
//                 ),
//               ),
//             ),

//             CustomButtonCard(
//               btnText: 'Continue',
//               isActive: false,
//               bgColor: Theme.of(context).primaryColor,
//               onTap: () {
//                 _navigateToRegisterScreen(context);
//               },
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }