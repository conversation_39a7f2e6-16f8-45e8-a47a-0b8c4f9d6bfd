import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

@immutable
class DocumentUploadResponse extends Equatable {
  const DocumentUploadResponse({
    required this.success,
    required this.message,
    this.documentId,
    this.uploadId,
    this.status,
    this.estimatedProcessingTime,
    this.nextSteps,
    this.errors,
  });

  final bool success;
  final String message;
  final String? documentId;
  final String? uploadId;
  final String? status;
  final Duration? estimatedProcessingTime;
  final List<String>? nextSteps;
  final List<String>? errors;

  @override
  List<Object?> get props => [
        success,
        message,
        documentId,
        uploadId,
        status,
        estimatedProcessingTime,
        nextSteps,
        errors,
      ];

  DocumentUploadResponse copyWith({
    bool? success,
    String? message,
    String? documentId,
    String? uploadId,
    String? status,
    Duration? estimatedProcessingTime,
    List<String>? nextSteps,
    List<String>? errors,
  }) {
    return DocumentUploadResponse(
      success: success ?? this.success,
      message: message ?? this.message,
      documentId: documentId ?? this.documentId,
      uploadId: uploadId ?? this.uploadId,
      status: status ?? this.status,
      estimatedProcessingTime: estimatedProcessingTime ?? this.estimatedProcessingTime,
      nextSteps: nextSteps ?? this.nextSteps,
      errors: errors ?? this.errors,
    );
  }

  // Helper methods
  bool get hasErrors => errors != null && errors!.isNotEmpty;
  bool get hasNextSteps => nextSteps != null && nextSteps!.isNotEmpty;
  bool get isProcessing => status?.toLowerCase() == 'processing';
  bool get isPending => status?.toLowerCase() == 'pending';

  // Factory constructors for common scenarios
  factory DocumentUploadResponse.success({
    required String message,
    String? documentId,
    String? uploadId,
    String? status,
    Duration? estimatedProcessingTime,
    List<String>? nextSteps,
  }) {
    return DocumentUploadResponse(
      success: true,
      message: message,
      documentId: documentId,
      uploadId: uploadId,
      status: status,
      estimatedProcessingTime: estimatedProcessingTime,
      nextSteps: nextSteps,
    );
  }

  factory DocumentUploadResponse.failure({
    required String message,
    List<String>? errors,
  }) {
    return DocumentUploadResponse(
      success: false,
      message: message,
      errors: errors,
    );
  }

  // Empty response singleton
  static final empty = DocumentUploadResponse(
    success: false,
    message: '',
  );
}
