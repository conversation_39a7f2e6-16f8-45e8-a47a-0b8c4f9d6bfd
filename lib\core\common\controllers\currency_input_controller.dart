import 'package:cbrs/core/common/global_variable.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

enum CurrencyType { usd, etb }

class CurrencyInputController {
  CurrencyInputController({
    required this.currencyType,
    required this.maxBalance,
    this.exchangeRate,
    this.defaultAmount,
    this.ignoreWalletAmountCheck = false,
    this.maxTransferLimit,
    this.minTransferLimit,
  }) {
    textController.addListener(_handleTextChange);

    if (defaultAmount != null) {
      textController.text =
          _addCurrencySymbol(_formatAmount(defaultAmount.toString()));
    }
  }
  final TextEditingController textController = TextEditingController();
  final CurrencyType currencyType;
  final double maxBalance;
  double? maxTransferLimit;
  double? minTransferLimit;
  final ValueNotifier<bool> isValidNotifier = ValueNotifier<bool>(false);
  final ValueNotifier<bool> exceedsBalanceNotifier = ValueNotifier<bool>(false);
  final ValueNotifier<bool> exceedsTransferLimitNotifier =
      ValueNotifier<bool>(false);
  final ValueNotifier<bool> showSecondaryAmountNotifier =
      ValueNotifier<bool>(false);
  final ValueNotifier<String> secondaryAmountNotifier =
      ValueNotifier<String>('');

  double? exchangeRate;
  double? defaultAmount;
  bool _ignoreTextChanges = false;
  bool ignoreWalletAmountCheck;
  bool enableDebugPrints = false;

  void _handleTextChange() {
    if (_ignoreTextChanges) return;

    final text = textController.text;

    if (text.isEmpty) {
      isValidNotifier.value = false;
      exceedsBalanceNotifier.value = false;
      exceedsTransferLimitNotifier.value = false;
      showSecondaryAmountNotifier.value = false;
      return;
    }

    try {
      final cleanAmount = text.replaceAll(RegExp(r'[^\d.]'), '');
      final inputAmount = double.parse(cleanAmount);

      // final exceedsBalance = inputAmount > (maxBalance ?? 0);
      final exceedsLimit =
          maxTransferLimit != null && inputAmount > maxTransferLimit!;
      final belowMinLimit =
          minTransferLimit != null && inputAmount < minTransferLimit!;
      // exceedsBalanceNotifier.value = exceedsLimit;

      // exceedsBalanceNotifier.value = exceedsBalance;
      exceedsTransferLimitNotifier.value = exceedsLimit;

      debugPrint(
        'exceedsLimitexceedsLimit $exceedsLimit',
      );
      if (enableDebugPrints && exceedsLimit) {
        debugPrint(
          'DEBUG: Exceeds transfer limit - Input: $inputAmount, Max: $maxTransferLimit',
        );
      }

      isValidNotifier.value = inputAmount > 0 &&
          //  !exceedsBalance &&
          !exceedsLimit &&
          !belowMinLimit;

      if (exchangeRate != null) {
        showSecondaryAmountNotifier.value = true;

        secondaryAmountNotifier.value = currencyType == CurrencyType.usd
            ? '${(inputAmount * exchangeRate!).toStringAsFixed(2)} ETB'
            : '\$${(inputAmount / exchangeRate!).toStringAsFixed(2)}';
      } else {
        showSecondaryAmountNotifier.value = false;
      }
    } catch (e) {
      isValidNotifier.value = false;
      exceedsBalanceNotifier.value = false;
      exceedsTransferLimitNotifier.value = false;
      showSecondaryAmountNotifier.value = false;
    }
  }

  void onKeyPressed(String value) {
    if (value == '⌫' || value == 'clear') {
      handleBackspace();
    } else {
      _handleNumberInput(value);
    }
  }

  void clearAll() {
    textController.text = '';
    _handleTextChange();
  }

  void handleBackspace() {
    if (textController.text.isEmpty) return;

    final currentAmount = textController.text.replaceAll(RegExp(r'[^\d.]'), '');

    if (currentAmount.isEmpty) return;

    final newAmount = currentAmount.substring(0, currentAmount.length - 1);

    if (newAmount.isEmpty) {
      textController.text = '';
      _handleTextChange();
      return;
    }

    try {
      _ignoreTextChanges = true;
      final formattedAmount = _formatAmount(newAmount);
      textController.text = _addCurrencySymbol(formattedAmount);
    } catch (e) {
      return;
    } finally {
      _ignoreTextChanges = false;
      _handleTextChange();
    }
  }

  void _handleNumberInput(String value) {
    var currentAmount = textController.text.replaceAll(RegExp(r'[^\d.]'), '');

    // Handle decimal point
    if (value == '.') {
      if (currentAmount.contains('.') || currentAmount.isEmpty) return;

      _ignoreTextChanges = true;
      if (currentAmount.isEmpty) {
        textController.text = _addCurrencySymbol('0.');
      } else {
        textController.text = _addCurrencySymbol('$currentAmount.');
      }
      _ignoreTextChanges = false;
      _handleTextChange();
      return;
    }

    // Prevent leading zeros
    if (value == '0' && currentAmount.isEmpty) {
      _ignoreTextChanges = true;
      textController.text = _addCurrencySymbol('0');
      _ignoreTextChanges = false;
      _handleTextChange();
      return;
    }

    // Don't allow input if current amount is just '0'
    if (currentAmount == '0' && value != '.') {
      return;
    }

    // Check if adding the new digit would exceed max length
    if (currentAmount.length >= 10) return;

    // Limit to 2 decimal places
    if (currentAmount.contains('.')) {
      final parts = currentAmount.split('.');
      if (parts.length > 1 && parts[1].length >= 2) return;
    }

    if (defaultAmount != null) {
      defaultAmount = null;
      currentAmount = '';
      textController.clear();
    }

    // Create new amount
    final newAmount = currentAmount + value;

    try {
      _ignoreTextChanges = true;
      final formattedAmount = _formatAmount(newAmount);
      textController.text = _addCurrencySymbol(formattedAmount);
    } catch (e) {
      // Ignore invalid inputs
    } finally {
      _ignoreTextChanges = false;
      _handleTextChange();
    }
  }

  String _formatAmount(String amount) {
    if (amount.contains('.')) {
      final parts = amount.split('.');
      final wholePart = _formatWithCommas(parts[0]);
      return '$wholePart.${parts[1]}';
    } else {
      return _formatWithCommas(amount);
    }
  }

  String _formatWithCommas(String number) {
    final parsed = int.tryParse(number.replaceAll(',', ''));
    if (parsed == null) return number;
    return NumberFormat.decimalPattern().format(parsed);
  }

  String _addCurrencySymbol(String amount) {
    return (GlobalVariable.currentlySelectedWallet ?? '') == 'USD'
        ?

        // currencyType == CurrencyType.usd ?
        '\$$amount'
        : '$amount ETB';
  }

  String get cleanAmount =>
      textController.text.replaceAll(RegExp(r'[^\d.]'), '');

  double get numericAmount {
    try {
      return double.parse(cleanAmount);
    } catch (e) {
      return 0;
    }
  }

  void setAmount(double amount) {
    _ignoreTextChanges = true;
    final formattedAmount = _formatAmount(amount.toString());
    textController.text = _addCurrencySymbol(formattedAmount);
    _ignoreTextChanges = false;
    _handleTextChange();
  }

  void dispose() {
    textController.removeListener(_handleTextChange);
    textController.dispose();
    isValidNotifier.dispose();
    exceedsBalanceNotifier.dispose();
    exceedsTransferLimitNotifier.dispose();
    showSecondaryAmountNotifier.dispose();
    secondaryAmountNotifier.dispose();
  }
}
