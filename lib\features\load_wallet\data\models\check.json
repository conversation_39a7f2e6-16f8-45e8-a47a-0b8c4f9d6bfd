{"statusCode": 200, "success": true, "data": {"id": "455ae0fb-67ef-437b-a125-ebf696d16fbb", "typeId": null, "senderId": null, "customerName": null, "senderName": "misge", "senderPhone": null, "senderEmail": null, "transactionOwner": "member", "sessionID": "https://payments.teletv.et/mpgs_test.html?id=SESSION0002369860856I0922928F74", "cardNumber": "512345xxxxxx0008", "paymentMethod": "MASTERCARD", "redirectURL": "https://assets.eaglelionsystems.com/loading.html", "mpgsReference": "************", "billRefNo": "************", "beneficiaryId": "6778de11de696d51fc7ddf10", "beneficiaryName": "auth rest test msige", "beneficiaryPhone": "+************", "beneficiaryAccountNo": null, "beneficiaryEmail": "<EMAIL>", "bankName": null, "bankCode": null, "bankId": null, "bankLogo": null, "senderAvatar": null, "beneficiaryAvatar": null, "senderConnectCode": null, "beneficiaryConnectCode": "XXCMZ7092K", "transactionType": "load_to_wallet", "orderId": null, "giftPackageId": null, "merchantId": null, "merchantTill": null, "merchantType": null, "paidUsing": null, "totalGiftPackageQty": null, "packageDiscountLevel": null, "packageDiscountId": null, "packageCampaignId": null, "billAmount": 1000, "originalCurrency": "USD", "serviceCharge": 0, "VAT": 0, "totalAmount": 1000, "changedCurrency": "USD", "exchangeRate": null, "paidAmount": 1000, "overPaid": false, "underPaid": false, "paymentDetails": {"3dsAcsEci": "02", "amount": 1000, "authentication": {"3ds": {"acsEci": "02", "authenticationToken": "kHyn+7YFi1EUAREAAAAvNUe6Hv8=", "transactionId": "1824c2ee-c52f-4a36-bba0-b16a464be5b2"}}, "authenticationStatus": "AUTHENTICATION_SUCCESSFUL", "authenticationVersion": "3DS2", "chargeback": {"amount": 0, "currency": "USD"}, "creationTime": "2025-05-30T12:00:50.164Z", "currency": "USD", "description": "Product Purchase", "device": {"browser": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "ipAddress": "**************"}, "id": "************", "lastUpdatedTime": "2025-05-30T12:00:55.795Z", "merchant": "TEST040000100132", "merchantAmount": 1000, "merchantCategoryCode": "4829", "merchantCurrency": "USD", "result": "SUCCESS", "sourceOfFunds": {"provided": {"card": {"brand": "MASTERCARD", "expiry": {"month": "9", "year": "28"}, "fundingMethod": "DEBIT", "nameOnCard": "misge", "number": "512345xxxxxx0008", "scheme": "MASTERCARD", "storedOnFile": "NOT_STORED"}}, "type": "CARD"}, "status": "CAPTURED", "totalAuthorizedAmount": 1000, "totalCapturedAmount": 1000, "totalDisbursedAmount": 0, "totalRefundedAmount": 0, "transaction": [{"authentication": {"3ds": {"acsEci": "02", "authenticationToken": "kHyn+7YFi1EUAREAAAAvNUe6Hv8=", "transactionId": "1824c2ee-c52f-4a36-bba0-b16a464be5b2"}, "3ds2": {"acsTransactionId": "4e5c2ed0-33d8-4e1c-a3fc-21a39a3e474a", "directoryServerId": "A999999999", "dsTransactionId": "1824c2ee-c52f-4a36-bba0-b16a464be5b2", "methodCompleted": false, "methodSupported": "SUPPORTED", "protocolVersion": "2.2.0", "requestorId": "MAS00001_INT_MPGS_MTTEST04000010013", "requestorName": "cashGo_test", "transactionStatus": "Y"}, "acceptVersions": "3DS1,3DS2", "channel": "PAYER_BROWSER", "method": "OUT_OF_BAND", "payerInteraction": "REQUIRED", "purpose": "PAYMENT_TRANSACTION", "redirect": {"domainName": "mtf.gateway.mastercard.com"}, "version": "3DS2"}, "device": {"browser": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "ipAddress": "**************"}, "merchant": "TEST040000100132", "order": {"amount": 1000, "authenticationStatus": "AUTHENTICATION_SUCCESSFUL", "chargeback": {"amount": 0, "currency": "USD"}, "creationTime": "2025-05-30T12:00:50.164Z", "currency": "USD", "description": "Product Purchase", "id": "************", "lastUpdatedTime": "2025-05-30T12:00:55.795Z", "merchantAmount": 1000, "merchantCategoryCode": "4829", "merchantCurrency": "USD", "status": "CAPTURED", "totalAuthorizedAmount": 0, "totalCapturedAmount": 0, "totalDisbursedAmount": 0, "totalRefundedAmount": 0, "valueTransfer": {"accountType": "NOT_A_TRANSFER"}}, "response": {"gatewayCode": "APPROVED", "gatewayRecommendation": "PROCEED"}, "result": "SUCCESS", "sourceOfFunds": {"provided": {"card": {"brand": "MASTERCARD", "expiry": {"month": "9", "year": "28"}, "fundingMethod": "DEBIT", "nameOnCard": "misge", "number": "512345xxxxxx0008", "scheme": "MASTERCARD"}}, "type": "CARD"}, "timeOfLastUpdate": "2025-05-30T12:00:53.550Z", "timeOfRecord": "2025-05-30T12:00:49.350Z", "transaction": {"acquirer": {"merchantId": "be<PERSON>han"}, "amount": 1000, "authenticationStatus": "AUTHENTICATION_SUCCESSFUL", "currency": "USD", "id": "trans-593", "stan": "0", "type": "AUTHENTICATION"}, "version": "73"}, {"authentication": {"3ds": {"acsEci": "02", "authenticationToken": "kHyn+7YFi1EUAREAAAAvNUe6Hv8=", "transactionId": "1824c2ee-c52f-4a36-bba0-b16a464be5b2"}, "3ds2": {"dsTransactionId": "1824c2ee-c52f-4a36-bba0-b16a464be5b2", "protocolVersion": "2.2.0", "transactionStatus": "Y"}, "transactionId": "trans-593", "version": "3DS2"}, "authorizationResponse": {"commercialCard": "888", "commercialCardIndicator": "3", "financialNetworkCode": "777", "posData": "1025100006600", "posEntryMode": "812", "processingCode": "003000", "responseCode": "00", "stan": "5621", "transactionIdentifier": "123456789"}, "device": {"browser": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "ipAddress": "**************"}, "gatewayEntryPoint": "CHECKOUT", "merchant": "TEST040000100132", "order": {"amount": 1000, "authenticationStatus": "AUTHENTICATION_SUCCESSFUL", "chargeback": {"amount": 0, "currency": "USD"}, "creationTime": "2025-05-30T12:00:50.164Z", "currency": "USD", "description": "Product Purchase", "id": "************", "lastUpdatedTime": "2025-05-30T12:00:55.795Z", "merchantAmount": 1000, "merchantCategoryCode": "4829", "merchantCurrency": "USD", "status": "CAPTURED", "totalAuthorizedAmount": 1000, "totalCapturedAmount": 1000, "totalDisbursedAmount": 0, "totalRefundedAmount": 0}, "response": {"acquirerCode": "00", "acquirerMessage": "Approved", "gatewayCode": "APPROVED"}, "result": "SUCCESS", "sourceOfFunds": {"provided": {"card": {"brand": "MASTERCARD", "expiry": {"month": "9", "year": "28"}, "fundingMethod": "DEBIT", "nameOnCard": "misge", "number": "512345xxxxxx0008", "scheme": "MASTERCARD", "storedOnFile": "NOT_STORED"}}, "type": "CARD"}, "timeOfLastUpdate": "2025-05-30T12:00:55.795Z", "timeOfRecord": "2025-05-30T12:00:55.641Z", "transaction": {"acquirer": {"batch": 20250530, "date": "0530", "id": "BBSC_S2I", "merchantId": "be<PERSON>han", "settlementDate": "2025-05-30", "timeZone": "+0300", "transactionId": "123456789"}, "amount": 1000, "authenticationStatus": "AUTHENTICATION_SUCCESSFUL", "authorizationCode": "005621", "currency": "USD", "id": "1", "receipt": "515012005621", "source": "INTERNET", "stan": "5621", "terminal": "00010577", "type": "PAYMENT"}, "version": "73"}]}, "invoiceURL": null, "billReason": "load_to_wallet", "paidDate": "2025-05-30T12:01:53.296Z", "FTNumber": "1748606513", "topupFrom": null, "walletFTNumber": "1748606513", "authorization_type": "PIN", "status": "COMPLETED", "isExpired": false, "coreResponse": null, "createdAt": "2025-05-30T12:00:06.658Z", "lastModified": "2025-05-30T12:01:53.311Z"}}