import 'dart:async';
import 'package:cbrs/core/common/widgets/custom_build_text.dart';
import 'package:cbrs/core/common/widgets/custom_pin_input.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/common/widgets/show_toast.dart';
import 'package:cbrs/features/transactions/application/bloc/transaction_bloc.dart';
import 'package:cbrs/features/transactions/domain/enums/transaction_type.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomOtpBottomSheet extends StatefulWidget {
  const CustomOtpBottomSheet({
    required this.onOtpVerified,
    required this.billRefNo,
    this.otpFor = 'TRANSACTION',
    this.transactionType,
    this.onResendSuccess,
    required this.focusNode,
    super.key,
  });

  final Function(String otpCode) onOtpVerified;
  final String billRefNo;
  final String otpFor;
  final TransactionType? transactionType;
  final VoidCallback? onResendSuccess;
  final FocusNode focusNode;

  @override
  State<CustomOtpBottomSheet> createState() => _CustomOtpBottomSheetState();
}

class _CustomOtpBottomSheetState extends State<CustomOtpBottomSheet> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  Timer? _timer;
  int _timeLeft = 59;
  bool _isResending = false;
  bool _isVerifying = false;

  @override
  void initState() {
    super.initState();
     WidgetsBinding.instance.addPostFrameCallback((_) {
    widget.focusNode.requestFocus();
  });
    _startTimer();
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    _timer?.cancel();
    setState(() {
      _timeLeft = 59;
    });
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_timeLeft == 0) {
        timer.cancel();
      } else {
        setState(() {
          _timeLeft--;
        });
      }
    });
  }

  void _handleResendOtp() {
    if (_timeLeft > 0 || _isResending) return;

    setState(() {
      _isResending = true;
    });

    context.read<TransactionBloc>().add(
          ResendOtpEvent(
            billRefNo: widget.billRefNo,
            otpFor: widget.otpFor,
          ),
        );
  }

  void _handleVerifyOtp() {
    if (_controller.text.length < 4 || _isVerifying) return;

    setState(() {
      _isVerifying = true;
    });

    try {
      final otpCode = int.parse(_controller.text);
      debugPrint(
        'So we were here with $otpCode and ${widget.otpFor} and ${widget.billRefNo}',
      );

      context.read<TransactionBloc>().add(
            VerifyOtpEvent(
              billRefNo: widget.billRefNo,
              otpFor: widget.otpFor,
              otpCode: otpCode,
            ),
          );
    } catch (e) {
      setState(() {
        _isVerifying = false;
      });
      CustomToastification(
        context,
        message: 'Please enter a valid OTP code',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<TransactionBloc, TransactionState>(
      listener: (context, state) {
        if (state is OtpResendSuccess) {
          setState(() {
            _isResending = false;
          });
          _startTimer();
          CustomToastification(
            context,
            message: 'OTP has been resent to your email',
            isError: false,
          );
          widget.onResendSuccess?.call();
        } else if (state is OtpResendError) {
          setState(() {
            _isResending = false;
            _controller.clear();
          });
          CustomToastification(
            context,
            message: state.message,
          );
        } else if (state is OtpVerificationSuccess) {
          setState(() {
            _isVerifying = false;
          });
          widget.onOtpVerified(_controller.text);
        } else if (state is OtpVerificationError) {
          debugPrint('this is whatsapp;');
          setState(() {
            _isVerifying = false;
          });
          CustomToastification(
            context,
            message: state.message,
          );
        }
      },
      child: IntrinsicHeight(
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          child: Container(
            clipBehavior: Clip.antiAlias,
            decoration: const ShapeDecoration(
              color: Color(0xFFFCFCFC),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(36),
                  topRight: Radius.circular(36),
                ),
              ),
            ),
            child: Column(
              children: [
                Center(
                  child: Container(
                    width: 64,
                    height: 6,
                    margin: EdgeInsets.only(
                      top: 16.h,
                      left: 16.w,
                      right: 16.w,
                      bottom: 8,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFFBCBCBC),
                      borderRadius: BorderRadius.circular(40),
                    ),
                  ),
                ),
                Flexible(
                  child: SingleChildScrollView(
                    physics: const ClampingScrollPhysics(),
                    child: Container(
                      color: Colors.white,
                      padding: EdgeInsets.only(
                        left: 16.w,
                        right: 16.w,
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const SizedBox(
                            height: 8,
                          ),
                          CustomBuildText(
                            text: 'Confirm transfer',
                            fontSize: 20.sp,
                            fontWeight: FontWeight.bold,
                          ),
                          const SizedBox(
                            height: 8,
                          ),
                          CustomBuildText(
                            text:
                                "We've sent a verification code to your email. Please enter it to confirm the transfer.",
                            textAlign: TextAlign.center,
                            color: Colors.black.withOpacity(0.3),
                            caseType: 'default',
                            fontSize: 14.sp,
                          ),
                          SizedBox(
                            height: 42.h,
                          ),
                          Center(
                            child: CustomBuildText(
                              text:
                                  '00:${_timeLeft.toString().padLeft(2, '0')} Sec',
                              fontSize: 14.sp,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                          SizedBox(
                            height: 36.h,
                          ),
                          IgnorePointer(
                            ignoring: _isVerifying,
                            child: CustomPinInput(
                              controller: _controller,
                              autoFocus: true,
                              pinFocusNode: widget.focusNode,
                            ),
                          ),
                          SizedBox(
                            height: 36.h,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CustomBuildText(
                                text: "Didn't receive code?",
                                color: const Color(0xFF7A7A7A),
                                fontSize: 14.sp,
                              ),
                              SizedBox(width: 8.w),
                              GestureDetector(
                                onTap: _timeLeft == 0 ? _handleResendOtp : null,
                                child: CustomBuildText(
                                  text: 'Resend OTP',
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.bold,
                                  color: _timeLeft == 0
                                      ? Theme.of(context).primaryColor
                                      : Theme.of(context)
                                          .primaryColor
                                          .withOpacity(0.5),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(
                            height: 40.h,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Container(
                  padding: EdgeInsets.only(
                    top: 16.h,
                    left: 16.w,
                    right: 16.w,
                    bottom: 20.h,
                  ),
                  decoration: const BoxDecoration(
                    color: Colors.white,
                  ),
                  child: CustomRoundedBtn(
                    btnText: 'Confirm',
                    isLoading: _isVerifying,
                    onTap: _handleVerifyOtp,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTransactionList({
    required String label,
    required String value,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        CustomBuildText(
          text: label,
          textAlign: TextAlign.center,
          fontSize: 14.sp,
          caseType: 'default',
        ),
        CustomBuildText(
          text: value,
          textAlign: TextAlign.center,
          fontSize: 14.sp,
          caseType: 'default',
        ),
      ],
    );
  }
}
