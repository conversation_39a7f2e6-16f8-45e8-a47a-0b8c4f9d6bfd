import 'dart:io';
import 'package:cbrs/core/errors/exceptions.dart';
import 'package:cbrs/core/errors/failures.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/identity_verification/data/datasources/identity_verification_remote_datasource.dart';
import 'package:cbrs/features/identity_verification/domain/entities/document_upload_response.dart';
import 'package:cbrs/features/identity_verification/domain/repositories/identity_verification_repository.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';

class IdentityVerificationRepositoryImpl
    implements IdentityVerificationRepository {
  const IdentityVerificationRepositoryImpl({
    required IdentityVerificationRemoteDataSource remoteDataSource,
  }) : _remoteDataSource = remoteDataSource;

  final IdentityVerificationRemoteDataSource _remoteDataSource;

  @override
  ResultFuture<DocumentUploadResponse> uploadDocument({
    required File frontPhoto,
    required File backPhoto,
    required File selfiePhoto,
    String documentType = 'id_card',
  }) async {
    try {
      debugPrint(
          '\n=== Identity Verification Repository - Upload Document ===');
      debugPrint('Document Type: $documentType');
      debugPrint('Front Photo: ${frontPhoto.path}');
      debugPrint('Back Photo: ${backPhoto.path}');
      debugPrint('Selfie Photo: ${selfiePhoto.path}');

      final result = await _remoteDataSource.uploadDocument(
        frontPhoto: frontPhoto,
        backPhoto: backPhoto,
        selfiePhoto: selfiePhoto,
        documentType: documentType,
      );

      debugPrint('Upload successful: ${result.success}');
      return Right(result);
    } on ValidationException catch (e) {
      debugPrint('Validation error: ${e.message}');
      return Left(ValidationFailure(message: e.message, errors: e.errors));
    } on ApiException catch (e) {
      debugPrint('API error: ${e.message}');
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      debugPrint('Network error: ${e.message}');
      return Left(NetworkFailure(message: e.message));
    } catch (e) {
      debugPrint('Unexpected error: $e');
      return Left(
          ServerFailure(message: 'Failed to upload document: ${e.toString()}'));
    }
  }
}
