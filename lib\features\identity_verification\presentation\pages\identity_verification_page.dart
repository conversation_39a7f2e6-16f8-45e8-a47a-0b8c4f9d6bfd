import 'package:cbrs/core/common/widgets/custom_menu_screen_cards.dart';
import 'package:cbrs/core/common/widgets/custom_page_header.dart';
import 'package:cbrs/core/common/widgets/custom_rounded_btn.dart';
import 'package:cbrs/core/res/media_res.dart';
import 'package:cbrs/features/identity_verification/presentation/bloc/identity_verification_bloc.dart';
import 'package:cbrs/features/identity_verification/presentation/bloc/identity_verification_state.dart';
import 'package:cbrs/features/identity_verification/presentation/pages/id_photo_instructions_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

class IdentityVerificationPage extends StatefulWidget {
  const IdentityVerificationPage({super.key});

  @override
  State<IdentityVerificationPage> createState() =>
      _IdentityVerificationPageState();
}

class _IdentityVerificationPageState extends State<IdentityVerificationPage> {
  bool _isDocumentUploaded = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Upgrade Account',
        ),
      ),
      body: SafeArea(
        bottom: true,
        child:
            BlocListener<IdentityVerificationBloc, IdentityVerificationState>(
          listener: (context, state) {
            if (state is IdentityVerificationSuccess) {
              setState(() {
                _isDocumentUploaded = true;
              });
            }
          },
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const CustomPageHeader(
                          pageTitle: 'Upgrade your Account',
                          description:
                              'Choose an option to upgrade your wallet.',
                        ),
                        SizedBox(height: 24.h),
                        CustomMenuScreenCards(
                          onTap: () {
                            // Handle upload document action
                          },
                          title: 'Upload Document',
                          description: 'Upload your identification document',
                          containerIcon: MediaRes.idCardIcon,
                          trailingWidget: Icon(
                            Icons.check_circle,
                            color: _isDocumentUploaded
                                ? Colors.green
                                : Colors.grey[500],
                            size: 24.sp,
                          ),
                        ),
                        SizedBox(height: 16.h),
                        CustomMenuScreenCards(
                          onTap: () {
                            // Handle scan ID action
                          },
                          title: 'Scan Your ID',
                          description:
                              'Scan your identification card using camera',
                          containerIcon: MediaRes.idCardIcon,
                          trailingWidget: Icon(
                            Icons.check_circle,
                            color: _isDocumentUploaded
                                ? Colors.green
                                : Colors.grey[500],
                            size: 24.sp,
                          ),
                        ),
                        SizedBox(height: 40.h),
                      ],
                    ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.fromLTRB(20.w, 16.h, 20.w, 12.h),
                child: RichText(
                  textAlign: TextAlign.start,
                  text: TextSpan(
                    style: GoogleFonts.outfit(
                      fontSize: 14.sp,
                      color: Colors.grey[600],
                    ),
                    children: [
                      const TextSpan(
                        text:
                            "Clicking the continue button means that I have read and agreed to the ",
                      ),
                      TextSpan(
                        text:
                            "user identity authentication information statement",
                        style: GoogleFonts.outfit(
                          fontSize: 14.sp,
                          color: Theme.of(context).primaryColor,
                          decoration: TextDecoration.underline,
                        ),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            // Handle the tap on the link
                            // For example, navigate to terms page or show a dialog
                            showDialog(
                              context: context,
                              builder: (context) => AlertDialog(
                                title: const Text(
                                    'Identity Authentication Information'),
                                content: const Text(
                                    'This document contains information about how your identity will be verified and how your data will be processed.'),
                                actions: [
                                  TextButton(
                                    onPressed: () => Navigator.pop(context),
                                    child: const Text('Close'),
                                  ),
                                ],
                              ),
                            );
                          },
                      ),
                    ],
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.fromLTRB(20.w, 0, 20.w, 24.h),
                child: CustomRoundedBtn(
                  btnText: 'Agree and Continue',
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const IdPhotoInstructionsPage(),
                      ),
                    );
                  },
                  isBtnActive: true,
                  isLoading: false,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
