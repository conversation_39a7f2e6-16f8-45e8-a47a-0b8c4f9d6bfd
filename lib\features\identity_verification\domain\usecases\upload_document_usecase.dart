import 'package:cbrs/core/usecases/usecase.dart';
import 'package:cbrs/core/utils/typedef.dart';
import 'package:cbrs/features/identity_verification/domain/entities/document_upload_response.dart';
import 'package:cbrs/features/identity_verification/domain/repositories/identity_verification_repository.dart';
import 'package:cbrs/features/identity_verification/domain/usecases/params/identity_verification_params.dart';

class UploadDocumentUseCase extends UsecaseWithParams<DocumentUploadResponse, DocumentUploadParams> {
  const UploadDocumentUseCase(this._repository);

  final IdentityVerificationRepository _repository;

  @override
  ResultFuture<DocumentUploadResponse> call(DocumentUploadParams params) =>
      _repository.uploadDocument(
        frontPhoto: params.frontPhoto,
        backPhoto: params.backPhoto,
        selfiePhoto: params.selfiePhoto,
        documentType: params.documentType,
      );
}
